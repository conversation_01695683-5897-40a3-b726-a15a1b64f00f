defmodule ReportsService.Repo.Migrations.CreateOrganizerSalesWebhook do
  use Ecto.Migration

  def up do
    create_if_not_exists table(:organizer_sales_webhook, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :organizer_id, :binary_id, null: false
      add :url, :string, null: false
      add :deleted_at, :utc_datetime

      timestamps(type: :utc_datetime)
    end

    create_if_not_exists index(:organizer_sales_webhook, [:organizer_id])

    create_if_not_exists unique_index(:organizer_sales_webhook, [:organizer_id, :url],
                           name: :organizer_sales_webhook_organizer_id_url_index
                         )
  end

  def down do
    drop_if_exists index(:organizer_sales_webhook, [:organizer_id])

    drop_if_exists index(:organizer_sales_webhook, [:organizer_id, :url],
                     name: :organizer_sales_webhook_organizer_id_url_index
                   )

    drop_if_exists table(:organizer_sales_webhook)
  end
end
