defmodule ReportsService.Repo.Migrations.CreateOrganizerSalesWebhookHistory do
  use Ecto.Migration

  def up do
    execute("DO $$ BEGIN
        CREATE TYPE organizer_sales_webhook_history_status AS ENUM ('CREATED', 'PROCESSING', 'SENDING', 'FINISHED', 'FAILED');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;")

    create_if_not_exists table(:organizer_sales_webhook_history, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :organizer_sales_webhook_id, :binary_id
      add :execution_id, :binary_id, null: false
      add :report_start_date, :utc_datetime, null: false
      add :report_end_date, :utc_datetime, null: false
      add :status_before, :organizer_sales_webhook_history_status
      add :status_after, :organizer_sales_webhook_history_status, null: false
      add :response, :map

      timestamps(type: :utc_datetime)
    end

    create_if_not_exists index(:organizer_sales_webhook_history, [:organizer_sales_webhook_id])
    create_if_not_exists index(:organizer_sales_webhook_history, [:execution_id, :inserted_at])
  end

  def down do
    drop_if_exists index(:organizer_sales_webhook_history, [:execution_id, :inserted_at])
    drop_if_exists index(:organizer_sales_webhook_history, [:organizer_sales_webhook_id])
    drop_if_exists table(:organizer_sales_webhook_history)
    execute("DROP TYPE IF EXISTS organizer_sales_webhook_history_status")
  end
end
