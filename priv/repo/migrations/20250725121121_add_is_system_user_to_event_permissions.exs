defmodule EventsService.Repo.Migrations.AddIsSystemPermissionToEventPermissions do
  use Ecto.Migration

  def change do
    alter table(:event_permissions) do
      add_if_not_exists :is_system_permission, :boolean, default: false, null: false
    end

    create_if_not_exists index(:event_permissions, [:is_system_permission])

    create_if_not_exists index(:event_permissions, [:event_id, :is_system_permission])
  end
end
