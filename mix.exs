defmodule AccountsService.MixProject do
  use Mix.Project

  @version "1.18.1"

  def project do
    [
      app: :accounts_service,
      version: @version,
      elixir: "~> 1.17",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps()
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {AccountsService.Application, []},
      extra_applications: [:logger, :runtime_tools, :db_seeds]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    # styler:sort
    pub_deps = [
      {:broadway, "~> 1.0"},
      {:broadway_cloud_pub_sub, "~> 0.8"},
      {:cors_plug, "~> 3.0"},
      {:credo, "~> 1.7.0", runtime: Mix.env() == :dev},
      {:db_seeds, "~> 0.0.1"},
      {:dialyxir, "~> 1.4", only: [:dev, :test], runtime: false},
      {:ecto_sql, "~> 3.12"},
      {:esbuild, "~> 0.5", runtime: Mix.env() == :dev},
      {:finch, "~> 0.13", override: true},
      {:floki, ">= 0.30.0", only: :test},
      {:gettext, "~> 0.20.0"},
      {:google_api_pub_sub, "~> 0.38.0"},
      {:goth, "~> 1.4"},
      {:hackney, "~> 1.20"},
      {:heroicons, "~> 0.5"},
      {:jason, "~> 1.4", override: true},
      {:joken, "~> 2.6"},
      {:json, "~> 1.4"},
      {:logger_json, "~> 6.0"},
      {:oauth2, "~> 2.1"},
      {:open_api_spex, "~> 3.20"},
      {:params, "~> 2.2"},
      {:phoenix, "~> 1.7.0"},
      {:phoenix_ecto, "~> 4.6"},
      {:phoenix_html, "~> 4.1"},
      {:phoenix_live_dashboard, "~> 0.8"},
      {:phoenix_live_reload, "~> 1.2", only: :dev},
      {:phoenix_live_view, "~> 0.20"},
      {:plug, "~> 1.15", override: true},
      {:plug_cowboy, "~> 2.5"},
      {:poison, "~> 6.0", override: true},
      {:postgrex, ">= 0.0.0"},
      {:proper_case, "~> 1.3"},
      {:sage, "~> 0.6.2"},
      {:scrivener_ecto, "~> 2.0"},
      {:styler, "~> 1.3", only: [:dev, :test], runtime: false},
      {:swoosh, "~> 1.16"},
      {:tailwind, "~> 0.2.3", runtime: Mix.env() == :dev},
      {:telemetry_metrics, "~> 1.0  "},
      {:telemetry_poller, "~> 1.0"},
      {:tesla, "~> 1.12"},
      {:timex, "~> 3.7.11"},
      {:ueberauth, "~> 0.10.8"},
      {:versioce, "~> 2.0", override: true},
      {:ymlr, "~> 5.1"}
    ]

    priv_deps =
      if Mix.env() == :dev do
        [
          {:ex_ikarus, path: "../ex_ikarus", override: true},
          {:ex_rbac, path: "../ex_rbac", override: true},
          {:ex_service_client, path: "../ex_service_client", override: true},
          {:secrets, path: "../ex_secrets", override: true}
        ]
      else
        [
          {:ex_ikarus, "~> 1.1.0", organization: "stagedates", override: true},
          {:ex_rbac, "~> 1.4", organization: "stagedates", override: true},
          {:ex_service_client, "~> 1.3", organization: "stagedates", override: true},
          {:ex_firebase_auth_plug, "~> 0.2.14", organization: "stagedates", override: true},
          {:secrets, "~> 0.1.0", organization: "stagedates", override: true}
        ]
      end

    pub_deps ++ priv_deps
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get", "ecto.setup", "assets.setup", "assets.build"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"],
      "assets.setup": ["tailwind.install --if-missing", "esbuild.install --if-missing"],
      "assets.build": ["tailwind default", "esbuild default"],
      "assets.deploy": ["tailwind default --minify", "esbuild default --minify", "phx.digest"]
    ]
  end
end
