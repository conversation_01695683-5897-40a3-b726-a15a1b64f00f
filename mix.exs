defmodule ReportsService.MixProject do
  use Mix.Project

  def project do
    [
      app: :reports_service,
      version: "1.9.3",
      elixir: "~> 1.17",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps()
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {ReportsService.Application, []},
      extra_applications: [:logger, :runtime_tools, :db_seeds]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    # styler:sort
    pub_deps =
      [
        {:cors_plug, "~> 3.0"},
        {:credo, "~> 1.7", only: [:dev, :test], runtime: false},
        {:csv, "~> 3.2"},
        {:db_seeds, "~> 0.0.1"},
        {:ecto_sql, "~> 3.11"},
        {:ex_money, "~> 5.16"},
        {:ex_money_sql, "~> 1.11"},
        {:finch, "~> 0.13", override: true},
        {:gettext, git: "https://github.com/jimpanse42/gettext", override: true},
        {:httpoison, "~> 2.2"},
        {:jason, "~> 1.4", override: true},
        {:joken, "~> 2.6"},
        {:logger_json, "~> 6.0"},
        {:nimble_csv, "~> 1.0"},
        {:oban, "2.18.3"},
        {:open_api_spex, "~> 3.21"},
        {:opentelemetry_exporter, "~> 1.6"},
        {:params, "~> 2.2"},
        {:pdf_generator, "~> 0.6.2"},
        {:phoenix, "~> 1.7.10"},
        {:phoenix_ecto, "~> 4.4"},
        {:plug_cowboy, "~> 2.5"},
        {:poison, "~> 6.0", override: true},
        {:postgrex, ">= 0.0.0"},
        {:proper_case, "~> 1.3"},
        {:scrivener_ecto, "~> 3.0"},
        {:styler, "~> 1.3.3", only: [:dev, :test], runtime: false},
        {:telemetry_metrics, "~> 1.0"},
        {:telemetry_poller, "~> 1.0"},
        {:timex, "~> 3.7.11"},
        {:tzdata, "~> 1.1.2", override: true},
        {:versioce, "~> 2.0", override: true},
        {:ymlr, "~> 5.1", override: true}
      ]

    priv_deps =
      if Mix.env() == :dev do
        [
          {:ex_firebase_auth_plug, path: "../ex_firebase_auth_plug", override: true},
          {:ex_ikarus, path: "../ex_ikarus", override: true},
          {:ex_rbac, path: "../ex_rbac", override: true},
          {:ex_service_client, path: "../ex_service_client", override: true},
          {:secrets, path: "../ex_secrets", override: true}
        ]
      else
        [
          {:ex_firebase_auth_plug, "~> 0.2.14", organization: "stagedates", override: true},
          {:ex_ikarus, "~> 2.0", organization: "stagedates", override: true},
          {:ex_rbac, "~> 1.0", organization: "stagedates", override: true},
          {:ex_service_client, "~> 0.9", organization: "stagedates", override: true},
          {:secrets, "~> 0.1.0", organization: "stagedates", override: true}
        ]
      end

    pub_deps ++ priv_deps
  end

  # Aliases are shortcuts or tasks specific to the current project.
  # For example, to install project dependencies and perform other setup tasks, run:
  #
  #     $ mix setup
  #
  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get", "ecto.setup"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"]
    ]
  end
end
