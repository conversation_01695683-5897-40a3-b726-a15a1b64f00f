# Changelog

All notable changes to this project will be documented in this file.

## [1.18.1] - 2025-08-29

### Bug Fixes

- Don't use lowercase for mail addresses (#198)

## [1.18.0] - 2025-08-19

### Features

- Add company to address (#194)

### Bug Fixes

- Userinfo for user without profile (#193)
- Allow to delete users without roles (#196)

### Miscellaneous Tasks

- Add PR template (#195)
- *(release)* V1.18.0 (#197)

## [1.17.1] - 2025-07-15

### Bug Fixes

- Adding maybe_get_firebase_error_message and returning 401 in update user API

### Miscellaneous Tasks

- Add spec
- Remove spec
- *(release)* V1.17.1

## [1.17.0] - 2025-07-14

### Features

- Add internal user id to all firebase users (#189)

### Miscellaneous Tasks

- *(release)* V1.17.0 (#190)

## [1.16.1] - 2025-07-07

### Bug Fixes

- Add else case for UserController.create_user to catch 400 error
- Add else case for UserController.create_user to catch 422 error
- AccountsService.Accounts.User to AccountsService.Users.User

### Miscellaneous Tasks

- *(release)* V1.16.1

## [1.16.0] - 2025-07-03

### Features

- *(DB)* Add db dump to set up a new database more easily (#182)
- Add user id to firebase auth user (#184)
- Add id to user response (#185)

### Bug Fixes

- Use correct pattern match for firebase error during user creation (#180)
- Cast store name (#181)

### Miscellaneous Tasks

- *(release)* V1.16.0 (#186)

## [1.15.0] - 2025-05-16

### Features

- *(seller_saga)* Add store_name to seller params (#170)
- Don't delete promoter or admin users (#179)

### Miscellaneous Tasks

- *(release)* V1.15.0 (#178)

## [1.14.0] - 2025-05-14

### Features

- *(seller_saga)* Don't add seller to seller group (#171)

### Bug Fixes

- *(create user)* Fetch error during user creation workflow (#172)
- Use correct IPv6 default (#174)
- Use correct pattern match during account creation (#175)
- Use correct error handling during user creation (#176)

### Refactor

- Read IP address from env (#173)

### Miscellaneous Tasks

- *(release)* V1.14.0 (#177)

## [1.13.0] - 2025-05-06

### Features

- *(user)* Add endpoint to delete user for testing purposes (#164)

### Bug Fixes

- Add pattern match for ecto changeset error during user creation (#165)

### Miscellaneous Tasks

- *(release)* V1.13.0 (#167)

## [1.12.2] - 2025-04-30

### Bug Fixes

- Use correct unique indexes (#162)

### Miscellaneous Tasks

- *(release)* V1.12.2 (#163)

## [1.12.1] - 2025-04-30

### Bug Fixes

- Allow birthdate for user registration (#160)

### Miscellaneous Tasks

- *(release)* V1.12.1 (#161)

## [1.12.0] - 2025-04-29

### Features

- Remove CloudRun deployment for dev environment (#158)

### Bug Fixes

- Add fallback to fix the registration for the app

### Miscellaneous Tasks

- *(release)* V1.12.0 (#159)

## [1.11.0] - 2025-04-24

### Features

- *(services_user_controller)* Handle both ID and external_id (#150)
- Security signin (#147)
- *(orders_orders_subscriber)* Add support for both user IDs (#151)
- *(userinfo)* Uuid user lookup (#152)
- *(authentication)* Add support for multiple Casdoor apps (#153)
- *(casdoor)* Add seller user creation (#156)

### Bug Fixes

- *(config)* Move endpoint config (#155)

### Miscellaneous Tasks

- Update mirrord script to use minikube context
- *(release)* V1.11.0

## [1.10.0] - 2025-03-20

### Features

- *(auth)* Support firebase and casdoor auth (#130)
- *(user)* Add casdoor security user (#145)

### Bug Fixes

- Remove firebase check for unauthenticated routes (#144)
- Handle update role (#146)

### Miscellaneous Tasks

- *(release)* V1.10.0 (#148)

## [1.9.0] - 2025-03-13

### Features

- *(policy_manager)* Add, update and remove policies on role update (#135)
- *(role_manager)* Add prefix to role IDs and separate policies (#142)

### Bug Fixes

- *(config)* Use correct casdoor secrets (#140)
- *(role_manager)* Add inspect to logs (#141)

### Miscellaneous Tasks

- *(release)* V1.9.0 (#143)

## [1.8.1] - 2025-03-11

### Miscellaneous Tasks

- *(release)* V1.8.1 (#139)

## [1.8.0] - 2025-03-11

### Features

- Add makefile and GitHub Actions workflow for release management (#131)
- *(user)* Add provider field to user JSON response (#132)
- *(user)* Add delete firebase user endpoint (#134)

### Bug Fixes

- *(services)* Decode URL-encoded user ID in parameters (#133)

### Miscellaneous Tasks

- *(release)* V1.8.0 (#136)

## [1.7.1] - 2025-02-26

### Bug Fixes

- Use correct message content for orders.order subscriber (#126)
- Use correct pattern match (#127)
- Use correct country syntax (#128)
- Read country_iso from address parameter

### Miscellaneous Tasks

- Update patch version (#129)

## [1.7.0] - 2025-02-25

### Features

- *(up)* Sync billing and delivery address during checkout (#123)

### Bug Fixes

- Admin user fetch expects a list of uid (#124)

### Miscellaneous Tasks

- Update minor version (#125)

## [1.6.3] - 2025-02-14

### Miscellaneous Tasks

- Change log level (#121)
- Update patch version (#122)

## [1.6.2] - 2025-02-14

### Features

- Remove table user_addresses (#119)

### Miscellaneous Tasks

- Update patch version (#120)

## [1.6.1] - 2025-02-12

### Features

- [**breaking**] Change user-address behaviour (#117)

### Miscellaneous Tasks

- Update patch version to v1.6.1 (#118)

## [1.6.0] - 2025-02-10

### Features

- Use DbSeeds instead of PhilColumns (#114)

### Miscellaneous Tasks

- Update styler to v1.3.3 and format code (#113)
- Update minor version to v1.6.0 (#116)

## [1.5.1] - 2025-02-05

### Bug Fixes

- Don't overwrite user_profile with nil values (#110)

### Other

- Add some debug loggings (#109)

### Miscellaneous Tasks

- Update patch version to v1.5.1 (#111)

## [1.5.0] - 2025-02-04

### Features

- [**breaking**] Add pubsub subscriber for orders.orders (#105)

### Bug Fixes

- Use correct order for application start
- Add alias, so that service can start again (#106)
- Add GCLOUD_PROJECT to Application env (#107)

### Miscellaneous Tasks

- Update minor version to v1.5.0 (#108)

## [1.4.1] - 2025-01-31

### Bug Fixes

- Use correct template for internal userinfo API (#103)

### Miscellaneous Tasks

- Update version to v1.4.1 (#104)

## [1.4.0] - 2025-01-31

### Features

- *(cloudbuild)* Update docker image location and deployment (#99)
- Make service ready for minikube (#100)
- Add location, billingAddress and deliveryAddress (#98)

### Bug Fixes

- *(create-user)* Transaction error handling (#97)
- Improve create_firebase_user, pattern matching "EMAIL_EXISTS" an… (#92)

### Miscellaneous Tasks

- Remove test function (#101)
- Update minor version to v1.4.0 (#102)

## [1.3.4] - 2025-01-22

### Features

- Adding "confirmPassword" to the filtered_params in phoenix logger

### Bug Fixes

- Improve passwort reset error handling and map to correct firebase error codes
- Using logger.debug instead of Tesla.Middleware.Logger.call to avoid sending hidden password via tesla
- Unused param

### Miscellaneous Tasks

- Use GitHub variable to configure reviewer-lottery (#90)
- Increase filter_sensitive_data stability
- Format
- Refactor module naming structure of tesla_logger_middleware.ex
- Bump version to 1.3.4

## [1.3.3] - 2025-01-16

### Features

- Create promoter user, user profile and user settings
- Migrate missing promoter users
- Move private function to bottom of file

### Miscellaneous Tasks

- Make credo happy
- Make credo happy
- Make credo happy
- Remove migration scripts
- Make credo happy
- Remove unused Logger
- Bump service to version 1.3.3 (#89)

## [1.3.2] - 2025-01-16

### Bug Fixes

- Update elixir action to latest version (#84)
- Create firebase user response (#85)

### Miscellaneous Tasks

- Update patch version (#88)

## [1.3.1] - 2025-01-02

### Features

- Add Jan to Reviewer Lottery (#78)

### Bug Fixes

- Add status 400 and 422 to create user API (#81)

### Miscellaneous Tasks

- Udpate deps (#82)
- Update patch version to v1.3.1 (#83)

## [1.3.0] - 2024-10-30

### Features

- *(accounts,svc,gke)* Trigger image change at gke [SI-221][SI-220]
- *(accounts,svc,gke)* Trigger image change at gke [SI-221][SI-220]
- *(accounts,svc,gke)* Annotate deployment [SI-221][SI-220]
- Oauth2 flow
- Add provider and rename firestore_id to external_id
- *(casdoor)* Create user

### Bug Fixes

- *(accounts,svc,gke)* Fix image uri [SI-221][SI-220]
- Add goth
- Merge
- Deps env
- Remove drop external id
- Use secrets module
- Use secrets lib
- Configure CORS Plug, allow  x-api-experimental header, update Ikarus (#76)

### Miscellaneous Tasks

- Cleanup
- Remove create user route
- Remove token from session
- Add create docs
- Add auth docs
- Cleanup
- Cleanup
- Clenaup
- Cleanup
- Fallback envs
- Move oauth2 config to runtime
- Remove unreachable code
- Change env name
- Update template envs
- Update version to 1.3.0 (#77)

## [1.2.8] - 2024-09-23

### Bug Fixes

- Add nil check to userinfo API (#71)

### Miscellaneous Tasks

- Trigger dev build
- Update patch verion to v1.2.8

## [1.2.7] - 2024-09-16

### Bug Fixes

- Change API response for user-profiles from an error to an empty list (#69)

### Miscellaneous Tasks

- Update patch version to v1.2.6

## [1.2.6] - 2024-09-16

### Bug Fixes

- Define API Version at compile time

### Miscellaneous Tasks

- Update patch version to v.1.2.6

## [1.2.5] - 2024-09-12

### Features

- *(user_profile)* Add user-profile APIs (#68)

### Miscellaneous Tasks

- Upate patch version to v1.2.5

## [1.2.4] - 2024-07-31

### Refactor

- Remove unused country_id column from addresses table (#66)

### Miscellaneous Tasks

- Update patch version to v1.2.4

## [1.2.3] - 2024-07-24

### Features

- Use country iso as 'foreign key' instead of the ID to make it more global (#65)

### Miscellaneous Tasks

- Update patch version to v1.2.3

## [1.2.2] - 2024-07-22

### Bug Fixes

- Check email not case sensitive (#64)

### Miscellaneous Tasks

- Update patch version to v1.2.2

## [1.2.1] - 2024-07-16

### Miscellaneous Tasks

- Update patch version to v1.2.1

## [1.2.0] - 2024-07-12

### Features

- Elixir-styler (#57)
- Add reviewer lottery (#61)
- Add elixir action

### Miscellaneous Tasks

- Bump elixir-styler (#59)
- Remove unused tests
- Run formatter
- Upate minor version to v1.2.0

## [1.1.5] - 2024-06-19

### Miscellaneous Tasks

- Remove Dennis from CODEOWNERS
- Update Logger
- Update patch version to v1.1.5

## [1.1.4] - 2024-05-03

### Features

- Add public as secondary db schema

### Miscellaneous Tasks

- Udpate db connection, use accounts schema
- Update env.template
- Bump patch version to v1.1.4

## [1.1.3] - 2024-05-02

### Miscellaneous Tasks

- Change local Phoenix port
- Set ex_firebase_auth_plug environment to local in dev config
- Bump patch version to v1.1.3

## [1.1.2] - 2024-04-26

### Features

- Fetch ex_service_client environment from env

### Miscellaneous Tasks

- Bump ex_service_client to 0.1.48
- Bump version to 1.1.2

## [1.1.1] - 2024-04-23

### Features

- Add /accounts/api/userinfo API for drop-in replacement
- Create firebase user
- Use last stable image for copied orders-service cloud run revision
- Remove cloudbuild v2 files
- Security signin
- Services endpoints
- Add logger json
- User documentation
- User documentation
- Extending minikube env
- Adding minikube env template
- Removing prod config check from minikube runtime
- Remove minikube env template and remove export from original template
- Config gcp logger and update elixir version
- Update dockerfile and remove base image
- Add Leon to CODEOWNERS

### Bug Fixes

- Make formatter run again
- Remove typos
- Use right secret for db connection
- Remove copy and paste error from restart cloudfile
- Remove OrdersService dependency from accounts-service
- Wrong sign in state
- Send sign in link
- Return ok if user does not exist
- Userinfo route
- Mails
- Unique constraint
- Joken config
- Firestore_id naming
- Create account
- Migration
- Update claims
- Add sub to userinfo
- Firebase client
- Get user with id token
- Add cache control header
- *(docker)* Fix docker for gracefull shutdown
- Run local
- Udpate elixir version in dockerfile
- Remove gke commands from cloudbild.yaml
- Add api doc push to cloudbuild
- Not use socket
- Gender_type migration
- Fetching runtime environment variables from envs
- Revert ENVIRONMENT env changes

### Other

- Firebase admin

### Miscellaneous Tasks

- Replace gender value by key for compatibility reasons
- Update cloudbuild files to run 2 parallel instances
- Update versioce
- Firebase api key from secret
- Add birthday
- Fallback firebase base url
- Add fallback frontend url
- Add old routes
- Fallback issuer
- Update deps
- Debug
- Revert debug changes
- Use json resp
- Update ex_service_client to 0.1.33
- Default gender type
- Remove debug logs
- Add firestore_id unique index
- Prepare for local development
- Debug
- Remove API_HOST env property form minikube env
- Add 'server: true' to config
- Add all codeowners
- Cleanup
- Bump ex_firebase_auth version to 0.2.13
- Bump version to 1.1.1

## [1.1.0] - 2023-10-09

### Features

- Delete deprecated tests
- Rename address attributes to make it equal with addresses in orders-service
- Resolve pr request changes and clean up code

### Bug Fixes

- Rename last_name to family_name (OpenID)

### Miscellaneous Tasks

- *(minor)* Update minor version with new database model

## [1.0.3] - 2023-10-09

### Features

- Copy priv/repo folder during cloudbuild deployment
- *(infra)* Use single step builds

### Bug Fixes

- ManagementEndpoint config
- Update path to the correct dockerfile

### Miscellaneous Tasks

- Added note
- Pump patch version (infrastructure deployment)

## [1.0.2] - 2023-09-20

### Features

- Create CODEOWNERS file
- Add developer nodes
- Use deleted_at instead of is_deleted in all tables

### Miscellaneous Tasks

- Use username in CODEOWNERS file
- Update version to 1.0.2

## [1.0.1] - 2023-09-15

### Features

- Internal mgmt api
- Add postgres controller
- *(promoter)* Add company name to promoters

### Bug Fixes

- Remove ',' and make compiler happy again
- Remove space and make ecto happy again
- Change nullable fields for promoter

### Miscellaneous Tasks

- Expose mgmt endpoint port
- Cleanup
- Update version from 1.0.0 to 1.0.1

## [1.0.0] - 2023-09-07

### Features

- Update patch version
- *(cloudbuild)* Copy migrations and seeds to cloudbuild instance

### Bug Fixes

- Read port for db connection from envs
- Read port for db connection from envs

### Miscellaneous Tasks

- Trigger build
- Add restart cr
- Add restart cr
- Add restart cr

## [0.2.1] - 2023-08-30

### Features

- Add migrations for users and user_settings tables
- Add db schemas for users and user_settings tables
- Add api to edit user settings
- *(api)* Add api to update user details
- Use system envs for database connection
- Add health check
- *(db)* Use accounts_schema_migrations to handle ecto migrations
- *(release)* Generate mix phx.gen.release
- Remove hex.pm dependency
- *(seeds)* Add phil columns seeds to mix deps
- *(seeds)* Configure phil_columns seeds and add first seed
- Update elixir and add .tool-versions file
- Add and configure credo
- Add is_deleted flag to donation_recipients and donation_recipients_employees
- Add countries into database table
- *(version)* Update (major) version to 1.0.0
- *(seeds)* Use phil_columns fork from Jessica's GitHub instead of Dennis'
- Update minor version

### Bug Fixes

- *(db)* Move user tables into accounts schema prefix
- Remove typo
- Remove typo in index names
- Remove hard coded config
- Fix web endpoint port
- Fix web endpoint port and debugger
- Make compiler work for MIX_ENV=prod, remove unused routes
- Revert debug commits
- Revert debug commits and generate new mix.lock
- Resolve merge conflicts, develop back into feature branch
- Use alias instead of import
- Remove typo
- Reomve is_deleted from required attributes
- Repair db migerations, add missing index, add if_not_exists
- Dynamic project name in base dockerfile
- Dynamic project name in base dockerfile
- Dynamic project name in base dockerfile
- Dynamic project name in base dockerfile
- Dynamic project name in base dockerfile
- Dynamic project name in base dockerfile
- Dynamic project name in base dockerfile

### Refactor

- Rename field name to username

### Documentation

- Add build tooling documentation
- Minor fix

### Miscellaneous Tasks

- Genesis
- Add joken config
- Add .env to gitignore
- Add make and docker files + config
- Add service path prefix to release
- Enhance build tooling
- Add build tooling
- Enhance project build
- Dockerfile.phx
- Revert Dockerfile.phx after migration
- Resolve github change requests
- Enhance manual build tooling
- Run mix format
- Run mix format
- Trigger ci
- Remove typo
- Run mix.format
- Make credeo happy and add moduletags
- Update some packages in mix.lock
- Remove unused import

<!-- generated by git-cliff -->
