import Config

# config/runtime.exs is executed for all environments, including
# during releases. It is executed after compilation and before the
# system starts, so it is typically used to load production configuration
# and secrets from environment variables or elsewhere. Do not define
# any compile-time configuration in here, as it won't be applied.
# The block below contains prod specific runtime configuration.

# ## Using releases
#
# If you use `mix release`, you need to explicitly enable the server
# by passing the PHX_SERVER=true when you start it:
#
#     PHX_SERVER=true bin/events_service start
#
# Alternatively, you can use `mix phx.gen.release` to generate a `bin/server`
# script that automatically sets the env var above.
if Secrets.get_secret("PHX_SERVER") do
  config :events_service, EventsServiceWeb.Endpoint, server: true
end

config :db_seeds,
  ensure_all_started: ~w(timex)a

config :events_service, :casdoor,
  organization_name: System.get_env("CASDOOR_ORGANIZATION_NAME", "stagedates"),
  promoter_domain_name: System.get_env("CASDOOR_PROMOTER_DOMAIN_NAME", "promoter"),
  user_model_name: System.get_env("CASDOOR_USER_MODEL_NAME", "user_model"),
  user_enforcer_name: System.get_env("CASDOOR_USER_ENFORCER_NAME", "user_enforcer")

config :events_service,
  frontend_url: System.get_env("FRONTEND_URL", "https://stagedates.com"),
  mail_pubsub_topic: System.get_env("MAIL_PUBSUB_TOPIC", "email.email"),
  pdf_pubsub_topic: System.get_env("PDF_PUBSUB_TOPIC", "generate-pdf-v1"),
  events_pubsub_topic: System.get_env("EVENTS_PUBSUB_TOPIC", "events.events"),
  ticket_categories_pubsub_topic: System.get_env("TICKET_CATEGORIES_PUBSUB_TOPIC", "events.ticket_categories"),
  sales_channels_pubsub_topic: System.get_env("SALES_CHANNELS_PUBSUB_TOPIC", "events.sales_channels"),
  imported_tickets_pubsub_topic: System.get_env("IMPORTED_TICKETS_PUBSUB_TOPIC", "events.imported_tickets"),
  tracking_link_url: Secrets.get_secret("TRACKING_LINK_URL"),
  tracking_link_api_key: Secrets.get_secret("TRACKING_LINK_API_KEY"),
  service_account: Secrets.get_secret("SERVICE_ACCOUNT"),
  ticket_secret: Secrets.get_secret("TICKET_SECRET"),
  ticket_subscription_name: System.get_env("TICKET_SUBSCRIPTION_NAME", "events-worker.orders.tickets"),
  imported_tickets_subscription_name:
    System.get_env("IMPORTED_TICKETS_PUBSUB_SUBSCRIPTION", "events-worker.orders.imported_tickets"),
  future_demand_events_subscription_name:
    System.get_env(
      "FUTURE_DEMAND_EVENTS_SUBSCRIPTION_NAME",
      "events-worker.future-demand.events"
    ),
  environment: String.to_atom(System.get_env("ENVIRONMENT", "prod")),
  spotify_client_id: Secrets.get_secret("SPOTIFY_CLIENT_ID"),
  spotify_secret: Secrets.get_secret("SPOTIFY_SECRET"),
  events_topic: System.get_env("EVENTS_TOPIC", "events.events"),
  gcloud_project: System.get_env("GCLOUD_PROJECT", "stdts-prod"),
  db_heartbeat_interval: String.to_integer(System.get_env("DB_HEARTBEAT_INTERVAL", "60")),
  sales_automation_interval: String.to_integer(System.get_env("SALES_AUTOMATION_INTERVAL", "30")),
  cloud_functions_files_subscription_name:
    System.get_env(
      "CLOUD_FUNCTIONS_FILES_SUBSCRIPTION_NAME",
      "events.worker-cloud_functions.files"
    ),
  geo_code_api_key: Secrets.get_secret("GOOGLE_GEOCODING_API_KEY")

config :ex_ikarus,
  environment: String.to_atom(System.get_env("ENVIRONMENT", "prod"))

config :ex_rbac, :casdoor,
  casdoor_certificate: Secrets.get_secret!("CASDOOR_CERTIFICATE"),
  application_id: Secrets.get_secret!("CASDOOR_APPLICATION_ID"),
  application_secret: Secrets.get_secret!("CASDOOR_APPLICATION_SECRET"),
  casdoor_url: System.get_env("CASDOOR_URL", "http://casdoor.casdoor.svc.cluster.local:8000"),
  username: Secrets.get_secret!("CASDOOR_USERNAME"),
  password: Secrets.get_secret!("CASDOOR_PASSWORD")

config :ex_service_client,
  environment: String.to_atom(System.get_env("ENVIRONMENT", "prod")),
  backend_endpoint: System.get_env("BACKEND_URL", "https://stagedates.com"),
  gke_endpoint: System.get_env("GKE_URL", "https://api.stagedates.com"),
  service_name: Secrets.get_secret("SERVICE_NAME"),
  service_client_id: Secrets.get_secret("SERVICE_CLIENT_ID"),
  service_client_secret: Secrets.get_secret("SERVICE_CLIENT_SECRET"),
  scheduler_token: Secrets.get_secret("SCHEDULER_TOKEN"),
  shortlink_url: System.get_env("SHORTLINK_URL", "https://link.stagedates.com")

config :google_api_pub_sub,
  # the base_url is used for publishing messages via pubsub emulator, it's only needed in Minikube, so we don't need
  # If desired, both `http:` and `https:` keys can be

  # the ENV in the .env.template or in the dev / prod systems
  # configured to run both http and https servers on
  # different ports.

  base_url: System.get_env("PUBSUB_BASE_URL", "https://pubsub.googleapis.com")

config :google_api_storage,
  # the base_url is used for uploading and downloading images via storage emulator, it's only needed in Minikube
  # If desired, both `http:` and `https:` keys can be

  # the ENV in the .env.template or in the dev / prod systems
  # configured to run both http and https servers on
  # different ports.
  base_url: System.get_env("GOOGLE_STORAGE_BASE_URL", "https://storage.googleapis.com")

config :opentelemetry, :processors,
  otel_batch_processor: %{
    exporter:
      {:opentelemetry_exporter,
       %{
         endpoints: [
           System.get_env(
             "OTEL_EXPORTER_OTLP_ENDPOINT",
             "http://opentelemetry-collector.opentelemetry.svc.cluster.local:4318"
           )
         ]
       }}
  }

config :opentelemetry, :resource, service: %{name: "EVENTS_SERVICE"}

config :opentelemetry,
  sampler:
    {:parent_based,
     %{
       root: {:trace_id_ratio_based, 0.10},
       remote_parent_sampled: :always_on,
       remote_parent_not_sampled: :always_off,
       local_parent_sampled: :always_on,
       local_parent_not_sampled: :always_off
     }}

config :opentelemetry,
  text_map_propagators: [:baggage, :trace_context],
  span_processor: :batch,
  traces_exporter: :otlp

# Initialize the Unleash client
config :unleash, Unleash,
  url: "#{Secrets.get_secret("UNLEASH_URL", "http://unleash-edge.unleash.svc.cluster.local:3063")}/api",
  appname: Secrets.get_secret("UNLEASH_APP_NAME", "events-service"),
  instance_id: Secrets.get_secret("UNLEASH_INSTANCE_ID", "events-service-prod"),
  strategies: Unleash.Strategies,
  custom_http_headers: [{"authorization", Secrets.get_secret("UNLEASH_AUTH_TOKEN")}],
  disable_client: false,
  disable_metrics: config_env() == :dev,
  app_env: config_env()

if config_env() == :prod do
  secret_key_base =
    Secrets.get_secret("SECRET_KEY_BASE") ||
      raise """
      environment variable SECRET_KEY_BASE is missing.
      You can generate one by calling: mix phx.gen.secret
      """

  host = Secrets.get_secret("PHX_HOST") || "example.com"
  path = Secrets.get_secret("PHX_PATH") || "/events"
  port = String.to_integer(Secrets.get_secret("PHX_PORT") || "4000")

  config :adyen,
    environment: "ADYEN_ENV" |> Secrets.get_secret() |> String.to_atom(),
    merchant_account: Secrets.get_secret("MERCHANT_ACCOUNT"),
    api_key: [
      checkout_api_key: Secrets.get_secret("ADYEN_PAYMENTS_API_KEY"),
      kyc_api_key: Secrets.get_secret("ADYEN_LEM_API_KEY"),
      balance_api_key: Secrets.get_secret("ADYEN_BALANCE_API_KEY")
    ],
    live_prefix: Secrets.get_secret("ADYEN_LIVE_URL_PREFIX"),
    theme_id: "ONBT422JV223222P5HTF9CN9VF3XQV",
    redirect_url: Secrets.get_secret("FRONTEND_URL"),
    balance_platform: Secrets.get_secret("ADYEN_BALANCE_PLATFORM")

  config :events_service, EventsService.Repo,
    hostname: Secrets.get_secret("POSTGRES_HOST"),
    username: Secrets.get_secret("POSTGRES_USER"),
    password: Secrets.get_secret("POSTGRES_PASSWORD"),
    database: Secrets.get_secret("POSTGRES_DB"),
    port: Secrets.get_secret("POSTGRES_PORT", "5432"),
    after_connect:
      {Postgrex, :query!, ["SET search_path TO #{System.get_env("POSTGRES_SCHEMA", "events")}, public", []]},
    migration_source: "events_service_schema_migrations",
    migration_timestamps: [type: :naive_datetime_usec],
    stacktrace: true,
    show_sensitive_data_on_connection_error: true,
    pool_size: 10

  config :events_service, EventsServiceWeb.Endpoint,
    url: [host: host, path: path],
    http: [
      # Enable IPv6 and bind on all interfaces.
      # Set it to  {0, 0, 0, 0, 0, 0, 0, 1} for local network only access.
      # See the documentation on https://hexdocs.pm/plug_cowboy/Plug.Cowboy.html
      #  config :events_service, EventsServiceWeb.ManagementEndpoint,
      # for details about using IPv6 vs IPv4 and loopback vs public addresses.
      #    url: [host: host, path: path],
      #    http: [
      #      # Enable IPv6 and bind on all interfaces.
      #      # Set it to  {0, 0, 0, 0, 0, 0, 0, 1} for local network only access.
      #      # See the documentation on https://hexdocs.pm/plug_cowboy/Plug.Cowboy.html
      #      # for details about using IPv6 vs IPv4 and loopback vs public addresses.
      #      ip: {0, 0, 0, 0, 0, 0, 0, 0},
      #      port: port + 1
      #    ],
      #    secret_key_base: secret_key_base
      compress: true,
      ip: {0, 0, 0, 0, 0, 0, 0, 0},
      port: port
    ],
    secret_key_base: secret_key_base

  config :events_service, :basic_auth,
    username: "migration",
    password: "1:F2#TcypK9~7JbLaqpk"

  config :events_service,
    cdn: Secrets.get_secret("CDN_URL"),
    gcloud: System.get_env("GCLOUD_PROJECT")

  config :ex_firebase_auth, :issuer, Secrets.get_secret("EX_FIREBASE_ISSUER")

  config :ex_firebase_auth_plug,
    environment: String.to_atom(System.get_env("ENVIRONMENT", "prod"))

  config :goth, json: Secrets.get_secret("SERVICE_ACCOUNT")

  config :joken, default_signer: Secrets.get_secret("BACKEND_API_TOKEN")
  config :joken, default_signer: Secrets.get_secret("BACKEND_API_TOKEN")

  config :seatsio,
    admin_key: Secrets.get_secret("SEATSIO_ADMIN_KEY"),
    public_default_workspace_key: Secrets.get_secret("SEATSIO_PUBLIC_DEFAULT_WORKSPACE_KEY"),
    private_default_workspace_key: Secrets.get_secret("SEATSIO_PRIVATE_DEFAULT_WORKSPACE_KEY")
end
