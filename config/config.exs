# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

config :db_seeds,
  ensure_all_started: ~w(timex)a

config :ex_cldr,
  json_library: Jason

config :ex_firebase_auth_plug, environment: :dev

config :ex_money,
  exchange_rates_retrieve_every: 300_000,
  api_module: Money.ExchangeRates.OpenExchangeRates,
  callback_module: Money.ExchangeRates.Callback,
  exchange_rates_cache_module: Money.ExchangeRates.Cache.Ets,
  preload_historic_rates: nil,
  retriever_options: nil,
  log_failure: :warning,
  log_info: :info,
  log_success: :debug,
  json_library: Jason,
  default_cldr_backend: ReportsService.Cldr,
  exclude_protocol_implementations: []

config :joken, default_signer: System.get_env("BACKEND_API_TOKEN")
config :joken, default_signer: System.get_env("BACKEND_API_TOKEN")

# Configures Elixir's Logger
config :logger, :console,
  level: :debug,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id]

# Configure phil columns for db seeds

# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

config :reports_service, Oban,
  prefix: "reports",
  repo: ReportsService.Repo,
  plugins: [
    Oban.Plugins.Pruner,
    {Oban.Plugins.Cron,
     crontab: [
       {"13 1 * * *", ReportsService.Workers.OrganizerSalesWebhook.InitWorker}
     ]}
  ],
  queues: [
    organizer_sales_webhook_init: 1,
    organizer_sales_webhook_report_generation: 5
  ]

config :reports_service, ReportsService.Repo,
  username: System.get_env("POSTGRES_USER"),
  password: System.get_env("POSTGRES_PASSWORD"),
  hostname: System.get_env("POSTGRES_HOST"),
  database: System.get_env("POSTGRES_DB"),
  port: System.get_env("POSTGRES_PORT"),
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: 10

# Configures the endpoint
config :reports_service, ReportsServiceWeb.Endpoint,
  url: [host: "localhost"],
  adapter: Phoenix.Endpoint.Cowboy2Adapter,
  render_errors: [
    formats: [json: ReportsServiceWeb.ErrorJSON],
    layout: false
  ],
  pubsub_server: ReportsService.PubSub,
  live_view: [signing_salt: "pIV5Lwvs"]

config :reports_service, ReportsServiceWeb.Gettext,
  priv: "priv/gettext/live",
  default_locale: "de",
  default_domain: "app",
  locales: ~w(de en)

config :reports_service,
  ecto_repos: [ReportsService.Repo],
  generators: [timestamp_type: :utc_datetime, binary_id: true]

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"
