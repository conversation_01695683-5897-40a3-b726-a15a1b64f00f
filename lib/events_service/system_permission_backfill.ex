defmodule EventsService.SystemPermissionBackfill do
  @moduledoc """
  Backfills the is_system_permission field for existing EventPermission records.

  This module identifies security users by the 'sec_' prefix in their user_document_id
  and sets is_system_permission to true for those records.

  Usage:
    EventsService.SystemPermissionBackfill.run()
    EventsService.SystemPermissionBackfill.run(batch_size: 200)
  """

  import Ecto.Query

  alias EventsService.Events.EventPermission
  alias EventsService.Repo

  require Logger

  @doc """
  Runs the backfill operation to set is_system_permission flag for existing security users.

  ## Options
    * `:batch_size` - Number of records to process in each batch (default: 100)

  ## Returns
    * `{:ok, updated_count}` - On successful completion
    * `{:error, reason}` - On failure
  """
  @spec run(keyword()) :: {:ok, non_neg_integer()} | {:error, any()}
  def run(opts \\ []) do
    batch_size = Keyword.get(opts, :batch_size, 100)

    Logger.info("Starting backfill of is_system_permission field...")

    try do
      query =
        from(ep in EventPermission,
          where: like(ep.user_document_id, ^"sec_%"),
          where: ep.is_system_permission == false or is_nil(ep.is_system_permission)
        )

      security_permissions = Repo.all(query)

      Logger.info("Found #{length(security_permissions)} security user permissions to update")

      if Enum.empty?(security_permissions) do
        Logger.info("No records to update. Backfill complete.")
        {:ok, 0}
      else
        perform_backfill(security_permissions, batch_size)
      end
    rescue
      error ->
        Logger.error("Backfill failed with error: #{inspect(error)}")
        {:error, error}
    end
  end

  defp perform_backfill(security_permissions, batch_size) do
    total_updated =
      security_permissions
      |> Enum.chunk_every(batch_size)
      |> Enum.with_index()
      |> Enum.reduce(0, fn {batch, index}, acc ->
        Logger.info("Processing batch #{index + 1} (#{length(batch)} records)...")

        batch_ids = Enum.map(batch, & &1.id)

        {updated_count, _} =
          Repo.update_all(from(ep in EventPermission, where: ep.id in ^batch_ids),
            set: [is_system_permission: true, updated_at: DateTime.utc_now()]
          )

        Logger.info("Updated #{updated_count} records in batch #{index + 1}")
        acc + updated_count
      end)

    Logger.info("Backfill complete! Updated #{total_updated} EventPermission records")

    {:ok, total_updated}
  end
end
