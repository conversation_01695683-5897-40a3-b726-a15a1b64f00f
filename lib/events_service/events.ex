defmodule EventsService.Events do
  @moduledoc """
  The Events context.
  """

  import Ecto.Changeset
  import Ecto.Query, warn: false

  alias Adyen.Services.BalancePlatform
  alias Ecto.Multi
  alias EventsService.Accounting
  alias EventsService.Accounting.PlatformFee
  alias EventsService.Addresses.Address
  alias EventsService.Channels.ChannelConfig
  alias EventsService.EventPermissions
  alias EventsService.Events.Artist
  alias EventsService.Events.DonationEvents
  alias EventsService.Events.EntranceArea
  alias EventsService.Events.Event
  alias EventsService.Events.EventCounter
  alias EventsService.Events.EventPermission
  alias EventsService.Events.Fee
  alias EventsService.Events.NameShortener
  alias EventsService.Events.TicketCategory
  alias EventsService.Events.TicketCategoryCounter
  alias EventsService.Events.Variant
  alias EventsService.Events.VariantCounter
  alias EventsService.Guestlists.Guestlist
  alias EventsService.Guestlists.GuestlistHistory
  alias EventsService.Guestlists.Invitation
  alias EventsService.Guestlists.InvitationApprovalRequest
  alias EventsService.Guestlists.InvitationAttendee
  alias EventsService.Guestlists.InvitationHistory
  alias EventsService.Guestlists.InvitationLink
  alias EventsService.Location
  alias EventsService.Location.Venue
  alias EventsService.Offers.Availability
  alias EventsService.Promotion.Voucher
  alias EventsService.Promotion.VoucherCounter
  alias EventsService.RBACClient
  alias EventsService.Repo
  alias EventsService.Seatsio, as: EventsSeatsio
  alias EventsService.Seller.Organizer
  alias EventsService.Seller.SalesPermission
  alias EventsService.Seller.Seller
  alias EventsService.SellerPermissions
  alias EventsService.Sellers
  alias EventsService.Tracking.TrackingLink
  alias EventsService.Tracking.TrackingPixel
  alias EventsService.Util.DateTime, as: DateTimeUtil
  alias EventsService.Util.EventPublicationHelper
  alias EventsService.Util.UUIDHelper
  alias EventsService.Vendor
  alias EventsService.Vendor.Promoter
  alias EventsService.Worker.EventCreationEmailWorker
  alias ExServiceClient.Services.AccountsService
  alias Seatsio.ChartReports
  alias Seatsio.Charts

  require Logger

  @doc """
  Returns the list of events.

  ## Examples

      iex> list_events()
      [%Event{}, ...]

  """
  def list_events(offset_id, page_size, category_name, promoter_id, venue_id, venue_city) do
    query =
      true
      |> get_events_overview_query()
      |> reject_draft()
      |> filter_visible()
      |> filter_category_name(category_name)
      |> filter_promoter_id(promoter_id)
      |> filter_venue_id(venue_id)
      |> filter_venue_city(venue_city)
      |> filter_ended_events()
      |> get_paginated_events(page_size, offset_id)

    Repo.all(query)
  end

  @spec list_promoter_events(
          promoter_id :: Ecto.UUID.t(),
          params :: map()
        ) :: Scrivener.Page.t()
  def list_promoter_events(promoter_id, params) do
    params = Map.put(params, :page_size, Map.get(params, :pageSize, 10))

    true
    |> get_complete_events_query()
    |> reject_draft()
    |> filter_visible()
    |> filter_promoter_id(promoter_id)
    |> filter_ended_events()
    |> order_by(^order_by_query_params(params))
    |> Repo.paginate(params)
  end

  @type seller_events_params :: %{
          seller_id: Ecto.UUID.t(),
          page: pos_integer(),
          page_size: pos_integer(),
          sort_by: String.t(),
          sort_direction: String.t(),
          query: String.t() | nil,
          start_date_from: DateTime.t() | nil,
          start_date_to: DateTime.t() | nil
        }

  @doc """
  Lists all events for a seller with pagination, sorting, and filtering.

  ## Parameters

    - params: A map containing the following fields:
      - seller_id: The ID of the seller
      - page: The page number (default: 1)
      - page_size: The number of items per page (default: 10)
      - sort_by: The field to sort by (title, start_date, venue_city, available_tickets)
      - sort_direction: The direction to sort (asc, desc)
      - query: Optional search query string
      - start_date_from: Optional filter for events starting from this date
      - start_date_to: Optional filter for events starting before this date

  ## Returns

    - {:ok, %Scrivener.Page{}} on success
    - {:error, reason} on failure
  """
  @spec get_seller_events(params :: seller_events_params()) :: Scrivener.Page.t()
  def get_seller_events(%{seller_id: seller_id} = params) do
    event_ids_query =
      from sp in SalesPermission,
        where: sp.seller_id == ^seller_id and is_nil(sp.deleted_at),
        where: sp.type == :EVENT,
        select: sp.type_id

    event_ids = Repo.all(event_ids_query)

    if Enum.empty?(event_ids) do
      %Scrivener.Page{
        entries: [],
        page_number: params.page,
        page_size: params.page_size,
        total_entries: 0,
        total_pages: 0
      }
    else
      # Prepare parameters for existing filter/sort functions
      search_params = if params[:query], do: %{search: params[:query]}, else: %{}
      date_params = %{start_date_from: params[:start_date_from], start_date_to: params[:start_date_to]}
      sort_params = %{order: params.sort_direction, sort: params.sort_by}

      true
      |> get_complete_events_query()
      |> where([event], event.id in ^event_ids)
      |> reject_draft()
      |> filter_visible()
      |> filter_ended_events()
      |> where(^filter_by_search_string(search_params))
      |> filter_by_date_range(date_params)
      |> filter_seller_events_by_venue_city(params[:venue_city])
      |> order_by(^order_by_query_params(sort_params))
      |> Repo.paginate(page: params.page, page_size: params.page_size)
    end
  end

  def get_popular_events(page_size, promoter_id) do
    Logger.info("Get Popular events with promoter_id #{inspect(promoter_id)}")

    get_sorted_popular_event_ids()
    |> get_list_of_popular_events(promoter_id)
    |> Enum.chunk_every(page_size)
    |> Enum.at(0)
  end

  def get_list_of_events(event_ids) do
    true
    |> get_complete_events_query()
    |> where([event], event.id in ^event_ids)
    |> Repo.all()
  end

  @spec list_paginated_promoter_events(bool(), String.t(), map()) :: Scrivener.Page.t()
  def list_paginated_promoter_events(admin?, user_id, params) do
    params = Map.put(params, :page_size, Map.get(params, :pageSize, 10))

    true
    |> get_complete_events_query(true)
    |> maybe_filter_by_user_id(admin?, user_id)
    |> where(^filter_by_search_string(params))
    |> where(^filter_by_query_params(params))
    |> order_by(^order_by_query_params(params))
    |> Repo.paginate(params)
  end

  @spec get_sorted_popular_event_ids() :: [Ecto.UUID.t()]
  def get_sorted_popular_event_ids do
    query =
      from(ec in EventCounter,
        as: :event_counter,
        join: e in Event,
        as: :event,
        on: ec.event_id == e.id,
        where: not e.is_draft,
        order_by: [desc: ec.sold_for_popular_events],
        select: ec.event_id
      )

    query
    |> filter_ended_events()
    |> Repo.all()
  end

  def get_list_of_popular_events(event_ids, promoter_id) do
    binary_ids = convert_events_id_to_binary(event_ids)

    true
    |> get_complete_events_query()
    |> where([event], event.id in ^event_ids)
    |> filter_visible()
    |> reject_draft()
    |> filter_promoter_id(promoter_id)
    |> order_by([event], fragment("array_position(?, ?)", ^binary_ids, event.id))
    |> Repo.all()
  end

  @spec get_all_active_event_ids() :: [Ecto.UUID.t()]
  def get_all_active_event_ids do
    query =
      from(e in Event,
        where: is_nil(e.closed_at),
        where: is_nil(e.deleted_at),
        select: e.id
      )

    Repo.all(query)
  end

  @spec get_all_event_ids() :: [Ecto.UUID.t()]
  def get_all_event_ids do
    query =
      from(e in Event,
        where: is_nil(e.deleted_at),
        select: e.id
      )

    Repo.all(query)
  end

  @spec get_variant_ids_for_event_id(event_id :: Ecto.UUID.t()) :: [Ecto.UUID.t()]
  def get_variant_ids_for_event_id(event_id) do
    query =
      from(
        v in Variant,
        where: v.event_id == ^event_id,
        where: is_nil(v.deleted_at),
        select: v.id
      )

    Repo.all(query)
  end

  @spec event_has_variant?(event_id :: Ecto.UUID.t() | nil, variant_id :: Ecto.UUID.t() | nil) :: boolean()
  def event_has_variant?(event_id, variant_id) when not is_nil(event_id) and not is_nil(variant_id) do
    query =
      from(
        v in Variant,
        where: v.event_id == ^event_id,
        where: v.id == ^variant_id,
        where: is_nil(v.deleted_at)
      )

    Repo.exists?(query)
  end

  def event_has_variant?(_event_id, _variant_id), do: false

  @spec get_voucher_ids_for_event_id(event_id :: Ecto.UUID.t()) :: [Ecto.UUID.t()]
  def get_voucher_ids_for_event_id(event_id) do
    query =
      from(
        v in Voucher,
        where: v.scope_id == ^event_id,
        where: v.scope == ^:EVENT,
        where: is_nil(v.deleted_at),
        select: v.id
      )

    Repo.all(query)
  end

  @spec get_ticket_category_ids_for_event_id(event_id :: Ecto.UUID.t()) :: [Ecto.UUID.t()]
  def get_ticket_category_ids_for_event_id(event_id) do
    query =
      from(
        tc in TicketCategory,
        where: tc.event_id == ^event_id,
        select: tc.id
      )

    Repo.all(query)
  end

  def convert_events_id_to_binary(event_ids) do
    Enum.reduce(event_ids, [], fn item, acc ->
      case Ecto.UUID.dump(item) do
        {:ok, id} -> acc ++ [id]
        _ -> acc
      end
    end)
  end

  def get_all_events(is_visible \\ true) do
    Repo.all(get_complete_events_query(is_visible))
  end

  def search_events(""), do: []

  def search_events(search_query) do
    query = from(event in Event, as: :event)
    variant_query = generate_variant_query(true)

    query
    |> join(
      :left,
      [event],
      ticket_cat in assoc(event, :ticket_categories)
    )
    |> join(:left, [event, ticket_cat], fees in assoc(event, :fees))
    |> preload([_event, ticket_cat, fees],
      fees: fees,
      ticket_categories: ticket_cat,
      variants: ^variant_query
    )
    |> preload(venue: [address: :country])
    |> where([event], is_nil(event.deleted_at))
    |> where([event], is_nil(event.closed_at))
    |> filter_query_param(search_query)
    |> filter_visible()
    |> reject_draft()
    |> filter_ended_events()
    |> Repo.all()
  end

  @spec get_edit_event(id :: Ecto.UUID.t(), is_visible :: boolean()) :: Event.t() | nil
  def get_edit_event(id, is_visible \\ true) do
    Logger.info("Get edit event for id: #{inspect(id)}")

    is_visible
    |> get_complete_events_query()
    |> where([event], event.id == ^id)
    |> Repo.one()
  end

  def get_promoter_details_event(id) do
    Logger.info("Get edit event for id: #{inspect(id)}")

    nil
    |> get_complete_events_query()
    |> where([event], event.id == ^id)
    |> Repo.one()
  end

  def get_promoter_uuid(promoter_id) do
    case Ecto.UUID.dump(promoter_id) do
      {:ok, _id} ->
        {:ok, promoter_id}

      _ ->
        case Repo.get_by(Promoter, firestore_id: promoter_id) do
          nil -> {:error, nil}
          promoter -> {:ok, promoter.id}
        end
    end
  end

  def get_show_event(id) do
    nil
    |> get_complete_events_query()
    |> where([event], event.id == ^id)
    |> Repo.one()
  end

  def get_show_public_event(id), do: id |> then(&generate_public_event_query(event_id: &1)) |> Repo.one()

  def get_show_public_event_by_short_code(string),
    do: string |> NameShortener.prepare_shortcode() |> then(&generate_public_event_query(short_code: &1)) |> Repo.one()

  def count_all_events do
    query = from(event in Event, select: count(event.id))

    case Repo.all(query) do
      [counts] -> counts
      _ -> 0
    end
  end

  def get_promoter_by_creator(user_id) do
    created_by_field = UUIDHelper.get_created_by_field(user_id)

    query =
      from(event in Event,
        where: field(event, ^created_by_field) == ^user_id
      )

    Repo.one(query)
  end

  def count_event_by_event_created_by(user_id) do
    created_by_field = UUIDHelper.get_created_by_field(user_id)

    query =
      from(event in Event,
        where: field(event, ^created_by_field) == ^user_id,
        select: count(event.id)
      )

    case Repo.all(query) do
      [counts] -> counts
      _ -> 0
    end
  end

  def count_event_by_promoter_id(promoter_id) do
    query =
      from(event in Event, where: event.promoter_id == ^promoter_id, select: count(event.id))

    case Repo.all(query) do
      nil -> 0
      [counts] -> counts
    end
  end

  def get_payout_event(id) do
    true
    |> get_complete_events_query()
    |> where([event], event.id == ^id and event.use_event_balance_account == true)
    |> preload([event], [:event_counter])
    |> Repo.one()
    |> case do
      nil -> {:error, :not_found}
      event -> {:ok, event}
    end
  end

  def get_event(id, preloads \\ []) do
    query =
      from(event in Event,
        where: is_nil(event.deleted_at),
        where: event.id == ^id,
        preload: ^preloads
      )

    Repo.one(query)
  end

  def get_open_event(id, preloads \\ []) do
    query =
      from(event in Event,
        where: is_nil(event.deleted_at),
        where: is_nil(event.closed_at),
        where: event.id == ^id,
        preload: ^preloads
      )

    Repo.one(query)
  end

  def get_event_promoter(id) do
    true
    |> get_complete_events_query()
    |> where([event], event.id == ^id)
    |> Repo.one()
  end

  def filter_query_param(query, search_query) do
    where(
      query,
      [event],
      fragment("? <% ?", ^search_query, event.title) or fragment("? <% ?", ^search_query, event.subtitle) or
        fragment("? <% ?", ^search_query, event.description) or fragment("? <% ?", ^search_query, event.category)
    )
  end

  def reject_draft(query), do: where(query, [event], not event.is_draft)
  def filter_visible(query), do: where(query, [event], event.is_visible)
  def filter_approved(query), do: where(query, [event], event.is_approved)

  def filter_category_name(query, nil), do: query

  def filter_category_name(query, category), do: where(query, [event], ilike(event.category, ^category))

  def filter_promoter_id(query, nil), do: query

  def filter_promoter_id(query, promoter_id) do
    where(query, [event], event.promoter_id == ^promoter_id)
  end

  def filter_venue_id(query, nil), do: query
  def filter_venue_id(query, venue_id), do: where(query, [event], event.venue_id == ^venue_id)

  def filter_venue_city(query, nil), do: query

  def filter_venue_city(query, venue_city) do
    query
    |> join(:inner, [event, _event_counter], venue in Venue, on: event.venue_id == venue.id)
    |> join(:inner, [_event, _event_counter, venue], address in Address, on: venue.address_id == address.id)
    |> where([_event, _event_counter, _venue, address], address.locality == ^venue_city)
  end

  def sort_by_starting_date(query), do: order_by(query, [event], asc: event.start_date)

  def get_paginated_events(query, page_size, nil) do
    query
    |> order_by([event], asc: event.start_date, asc: event.id)
    |> limit(^page_size)
  end

  def get_paginated_events(query, page_size, offset_id) do
    case Repo.get_by(Event, id: offset_id) do
      nil ->
        query |> order_by([event], asc: event.start_date) |> limit(^page_size)

      offset_event ->
        query
        |> where(
          [event],
          event.start_date > ^offset_event.start_date or
            (event.start_date == ^offset_event.start_date and event.id > ^offset_id)
        )
        |> order_by([event], asc: event.start_date, asc: event.id)
        |> limit(^page_size)
    end
  end

  def get_event_by_firestore_id(firestore_id) do
    true
    |> get_complete_events_query()
    |> where([event], event.firestore_id == ^firestore_id)
    |> Repo.one()
  end

  @doc """
  Creates a event.

  ## Examples

      iex> create_event(user_id, %{field: value})
      {:ok, %Event{}}

      iex> create_event(user_id, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_event(user_id, attrs \\ %{}, seller_id \\ nil) do
    Logger.info("Create event")

    with {:ok, %Promoter{} = promoter} <- fetch_promoter(user_id, seller_id) do
      %Venue{name: venue_name} = Location.get_venue(attrs["venueId"])
      attrs = Map.put(attrs, "venueName", venue_name)
      created_by_field = UUIDHelper.get_created_by_field(user_id)

      payload =
        attrs
        |> Event.prepare_attrs()
        |> Map.put("promoter_id", promoter.id)
        |> Map.put(Atom.to_string(created_by_field), user_id)
        |> Map.put("slug", get_slug(attrs))
        |> Map.put("short_code", NameShortener.generate_unique_shortcode())

      result =
        Multi.new()
        |> Multi.insert(:event, Event.changeset(%Event{}, payload))
        |> insert_artists(attrs["artists"])
        |> insert_fees(attrs["fees"])
        |> Multi.insert(:event_donation, fn %{event: inserted_event} ->
          payload = %{
            "donation_recipient_id" => "7e04bd00-cc95-474c-844b-1185e2561b3a",
            "amount" => 3.0,
            "type" => "percentage",
            "event_id" => inserted_event.id
          }

          DonationEvents.changeset(%DonationEvents{}, payload)
        end)
        |> add_event_sales_permissions(promoter.id)
        |> Multi.run(:balance_account, fn _repo, %{event: event} = _changes ->
          create_balance_account_for_event(promoter, event)
        end)
        |> Multi.update(:update_event, fn %{balance_account: balance_account, event: event} ->
          Event.changeset(event, %{"balance_account_id" => balance_account["id"]})
        end)
        |> Multi.run(:create_security_user, fn _repo, _changes -> create_security_user() end)
        |> Multi.insert(:event_promoter_permission, fn %{event: inserted_event} ->
          payload = %{
            "role" => [:EVENT_ADMIN],
            "event_id" => inserted_event.id,
            "user_document_id" => promoter.created_by_document_id,
            "user_id" => promoter.created_by,
            "inherited_from_seller?" => false,
            "is_system_permission" => false
          }

          EventPermission.changeset(%EventPermission{}, payload)
        end)
        |> SellerPermissions.add_seller_permissions_to_event(:event)
        |> Accounting.add_seller_platform_fees_to_event(seller_id, :event)
        |> Multi.insert(:event_permission, fn %{
                                                create_security_user: security,
                                                event: inserted_event
                                              } ->
          security_id = security["id"]
          user_id_field = security_id |> UUIDHelper.get_user_id_field() |> Atom.to_string()

          payload = %{
            "role" => [:SECURITY],
            "event_id" => inserted_event.id,
            "inherited_from_seller?" => false,
            "is_system_permission" => true,
            user_id_field => security_id
          }

          EventPermission.changeset(%EventPermission{}, payload)
        end)
        |> add_seats_and_chart_key_to_multi(promoter)
        |> Multi.run(:seats_variants, fn _repo, %{event: event} ->
          maybe_import_chart_categories_into_variants(event)
        end)
        |> Repo.transaction()

      case result do
        {:ok, %{update_event: event}} ->
          Logger.debug("Event created with event = #{inspect(event)}")
          {:ok, event}

        {:error, failed_operation, failed_value, _changes_so_far} ->
          Logger.error("Failed to create event in step #{inspect(failed_operation)} and error #{inspect(failed_value)}")

          {:error, failed_value}
      end
    end
  end

  def create_security_user do
    case AccountsService.create_security_user() do
      {:ok, security} ->
        {:ok, security}

      _ ->
        Logger.critical("Can't create a security user")
        {:error, :not_created}
    end
  end

  @doc """
  Updates a event.

  ## Examples

      iex> update_event(event, %{field: new_value})
      {:ok, %Event{}}

      iex> update_event(event, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_event(%Event{closed_at: closed_at, is_draft: is_draft} = event, attrs)
      when is_nil(closed_at) or is_draft do
    # We check if any of the fields related to the slug are updated. If none are updated, the "slug" in the attributes
    # is deleted so that a user cannot manually change the slug with a malicious API request.
    # For example, if an attacker were to enter "slug" and "title",
    # the "slug" entered by the user would be overwritten with the calculated one.
    attrs =
      case {attrs["venueId"], attrs["title"], attrs["startDate"]} do
        {nil, nil, nil} -> Map.delete(attrs, "slug")
        _ -> Map.put(attrs, "slug", add_slug_update(event, attrs))
      end

    Multi.new()
    |> Multi.update(:event, Event.update_changeset(event, Event.prepare_update_attrs(attrs, event)))
    |> maybe_update_balance_account(event, attrs)
    |> maybe_update_artist(attrs["artists"], event)
    |> Repo.transaction()
    |> case do
      {:ok, %{event: event}} ->
        {:ok, event}

      {:error, failed_operation, failed_value, _changes_so_far} ->
        Logger.error("Failed to update event in step #{inspect(failed_operation)} and error #{inspect(failed_value)}")

        {:error, failed_value}
    end
  end

  def update_event(%Event{id: event_id}, _attrs) do
    Logger.info("Try to edit event #{inspect(event_id)}, but the event is already closed.")

    {:error, :closed_event}
  end

  # When publishing a draft, we need to remove all previous generated data (tickets, etc) so the event has a clean slate
  def publish_draft_event(%Event{is_draft: true} = event) do
    with :ok <- validate_event_start_date_for_publication(event),
         :ok <- validate_event_admission_date_for_publication(event),
         {_, :ok} <-
           {:publish_draft_msg,
            EventPublicationHelper.publish_event_draft_msg(
              EventsServiceWeb.EventJSON.to_internal_event_details_dto(%{event: event})
            )},
         {_, {:ok, %{set_draft_attrs: %Event{} = event}}} <- {:purge_draft_data, do_publish_draft_event(event)},
         {_, {:ok, event}} <- {:maybe_transfer_seating_plan, maybe_transfer_seating_plan(event)} do
      {:ok, event}
    else
      {:purge_draft_data, {:error, failed_operation, failed_value, _changes_so_far}} ->
        Logger.critical(
          "Failed to purge draft data in step #{inspect(failed_operation)} and error #{inspect(failed_value)}"
        )

        {:error, failed_value}

      {:maybe_transfer_seating_plan, {:error, reason}} ->
        Logger.critical("Failed to transfer seating plan in step #{inspect(reason)}")
        {:error, reason}

      {_, reason} ->
        Logger.critical("Failed to publish event draft publish message: #{inspect(reason)}")
        {:error, reason}
    end
  end

  def publish_draft_event(%Event{id: event_id, closed_at: nil}) do
    Logger.info("Tried to publish event #{inspect(event_id)}, but the event is not in draft mode.")
    {:error, :no_draft}
  end

  def publish_draft_event(%Event{id: event_id}) do
    Logger.info("Tried to publish event #{inspect(event_id)}, but the event is already closed.")
    {:error, :closed_event}
  end

  def approve_event(event_id, payload) do
    event = get_event(event_id)
    Repo.update(Event.approve_changeset(event, payload))
  end

  def publish_event(event_id, payload) do
    event = get_event(event_id)
    Repo.update(Event.publish_changeset(event, payload))
  end

  def finalize_event(event, payload) do
    result =
      Multi.new()
      |> Multi.update_all(
        :availabilities,
        fn _changes ->
          now = DateTime.utc_now()

          from(a in Availability,
            where: a.event_id == ^event.id,
            update: [set: [valid_until: ^now, updated_at: ^now]]
          )
        end,
        []
      )
      |> Multi.update(:update_event, Event.finalize_changeset(event, payload))
      |> Repo.transaction()

    case result do
      {:ok, %{update_event: event}} ->
        Logger.info("Event updated with event = #{inspect(event)}")
        {:ok, event}

      {:error, failed_operation, failed_value, _changes_so_far} ->
        Logger.error("Failed to update event in step #{inspect(failed_operation)} and error #{inspect(failed_value)}")

        {:error, failed_value}
    end
  end

  def find_events_by_user_document_id(user_id) do
    user_field = if uuid?(user_id), do: :user_id, else: :user_document_id

    query =
      from(ep in EventPermission,
        where:
          field(ep, ^user_field) == ^user_id and
            (^"EVENT_ADMIN" in ep.role or ^"SECURITY" in ep.role),
        select: ep.event_id
      )

    query
    |> Repo.all()
    |> get_list_of_events()
  end

  def find_user_promoter(user_id, preloads \\ []) do
    user_field = if uuid?(user_id), do: :user_id, else: :user_document_id

    query =
      from(ep in EventPermission,
        where: field(ep, ^user_field) == ^user_id and ^"EVENT_ADMIN" in ep.role,
        join: e in Event,
        on: e.id == ep.event_id,
        select: e.promoter_id
      )

    Repo.all(from(p in Promoter, where: p.id in subquery(query), preload: ^preloads))
  end

  def get_event_permission(user_id, event_id) do
    user_field = if uuid?(user_id), do: :user_id, else: :user_document_id

    Repo.one(
      from(ep in EventPermission,
        where: ep.event_id == ^event_id and field(ep, ^user_field) == ^user_id
      )
    )
  end

  @spec has_access(
          events_id :: Ecto.UUID.t(),
          permission :: String.t(),
          user_id :: String.t(),
          seller_id :: String.t() | nil,
          role :: [String.t()],
          provider :: :FIREBASE | :CASDOOR
        ) ::
          boolean()
  def has_access(event_id, permission, user_id, seller_id \\ nil, roles \\ [], provider \\ :FIREBASE)

  def has_access(event_id, permission, user_id, seller_id, roles, :FIREBASE) do
    cond do
      admin_user?(roles) ->
        true

      not is_nil(seller_id) ->
        seller_and_user_access?(event_id, permission, user_id, seller_id)

      true ->
        user_access?(event_id, permission, user_id)
    end
  end

  # The user_id needs to be owner_name/user_name for CASDOOR as this is used by casdoor to check permissions.
  # Call this function to check permissions for a CASDOOR user with its actual user_id will not work.
  def has_access(event_id, permission, user_id, _seller_id, _roles, :CASDOOR) do
    # Even though Casdoor permissions have to be created with the first letter capitalized,
    # when checking user permissions, we need to convert the permission to lowercase
    permission =
      case permission do
        "event.edit" -> "write"
        "event.create" -> "write"
        "event.view" -> "read"
        _ -> "admin"
      end

    permission = [
      RBACClient.promoter_domain_name(),
      EventPermissions.get_event_permission_name(event_id),
      permission
    ]

    RBACClient.authorized?(user_id, permission)
  end

  @spec find_user_permission(event_permission :: EventPermission.t() | nil, permission :: String.t() | nil) ::
          String.t() | nil
  def find_user_permission(nil, _permission), do: nil
  def find_user_permission(%EventPermission{role: _roles} = _event_permission, nil), do: nil

  def find_user_permission(%EventPermission{role: roles}, permission) do
    default_permissions =
      Enum.map(ExRBAC.default_permissions(), fn item -> Map.put(item, :name, String.upcase(Map.get(item, :name))) end)

    roles
    |> Enum.reduce([], fn role_name, acc ->
      key = Atom.to_string(role_name)
      role = Enum.find(default_permissions, fn permission_item -> permission_item.name == key end)
      role.permissions ++ acc
    end)
    |> List.flatten()
    |> Enum.find(fn item -> item == permission end)
  end

  @spec get_security_user_by_event_id(Ecto.UUID.t()) :: EventPermission.t() | nil
  @spec get_security_user_by_event_id(Ecto.UUID.t(), Ecto.UUID.t() | nil) :: EventPermission.t() | nil
  def get_security_user_by_event_id(event_id, entrance_area_id \\ nil)

  def get_security_user_by_event_id(event_id, nil) do
    query =
      from(ep in EventPermission,
        left_join: ea in EntranceArea,
        on:
          ea.event_id == ep.event_id and
            ((not is_nil(ep.user_document_id) and ea.user_document_id == ep.user_document_id) or
               (not is_nil(ep.user_id) and ea.user_id == ep.user_id)),
        where: ep.event_id == ^event_id and ep.is_system_permission == true,
        where: is_nil(ea)
      )

    Repo.one(query)
  end

  def get_security_user_by_event_id(event_id, entrance_area_id) do
    query =
      from(ep in EventPermission,
        inner_join: ea in EntranceArea,
        on:
          ea.event_id == ep.event_id and
            ((not is_nil(ep.user_document_id) and ea.user_document_id == ep.user_document_id) or
               (not is_nil(ep.user_id) and ea.user_id == ep.user_id)),
        where: ep.event_id == ^event_id and ep.is_system_permission == true,
        where: ea.id == ^entrance_area_id
      )

    Repo.one(query)
  end

  def get_variant(variant_id) do
    Repo.get(Variant, variant_id)
  end

  @spec get_variants_by_availability_id(availability_id :: Ecto.UUID.t()) :: [Variant.t()]
  def get_variants_by_availability_id(availability_id) do
    query =
      from(variant in Variant,
        where: variant.availability_id == ^availability_id,
        where: is_nil(variant.deleted_at)
      )

    Repo.all(query)
  end

  def insert_event_creation_mail_job(email, event_id) do
    %{email: email, event_id: event_id}
    |> EventCreationEmailWorker.build()
    |> Oban.insert!()
  end

  def get_all_events_ids do
    query =
      from(e in Event,
        where: is_nil(e.deleted_at) and is_nil(e.closed_at),
        select: e.id
      )

    Repo.all(query)
  end

  def get_events_to_finalize do
    utc_now = DateTime.utc_now()
    utc_five_days_ago = DateTime.add(utc_now, -120, :hour)
    utc_five_and_a_half_days_ago = DateTime.add(utc_now, -132, :hour)

    query =
      from(e in Event,
        where: (is_nil(e.end_date) and e.start_date < ^utc_five_and_a_half_days_ago) or e.end_date < ^utc_five_days_ago,
        where: is_nil(e.deleted_at),
        where: is_nil(e.closed_at)
      )

    Repo.all(query)
  end

  def get_event_total_sales(%Event{event_counter: %EventCounter{total_sales: total_sales}}), do: total_sales
  def get_event_total_sales(_), do: 0

  # used when passed in attrs from frontend
  def get_slug(%{"title" => title, "startDate" => start_date, "venueName" => venue_name}) when is_binary(venue_name) do
    date = DateTimeUtil.to_default_string(start_date, :slug)

    [title, venue_name, date]
    |> Enum.join("-")
    |> Slug.slugify()
  end

  # used when passed in an event from db
  def get_slug(%Event{title: title, venue: %{name: venue_name}, start_date: start_date}) do
    get_slug(%{"title" => title, "startDate" => start_date, "venueName" => venue_name})
  end

  def get_slug(event) do
    Logger.warning("Couldn't generate slug from event: #{inspect(event)}")
    nil
  end

  @spec get(Ecto.UUID.t()) :: Event.t() | nil
  @spec get(Ecto.UUID.t(), Keyword.t()) :: Event.t() | nil
  def get(id, preloads \\ []) do
    query =
      from(event in Event,
        where: is_nil(event.deleted_at),
        where: event.id == ^id,
        preload: ^build_preload_queries(preloads)
      )

    Repo.one(query)
  end

  @doc """
  Lists paginated promoter events filtered by seller organizer permissions.

  Only shows events if the seller is an Organizer and its Promoter's created_by_document_id
  has EventPermission to the event.
  """
  @spec list_paginated_promoter_events_by_seller(bool(), String.t(), String.t(), map()) :: Scrivener.Page.t()
  def list_paginated_promoter_events_by_seller(admin?, user_id, seller_id, params) do
    organizer_event_ids_query = get_organizer_event_ids_query(seller_id)
    params = Map.put(params, :page_size, Map.get(params, :pageSize, 10))

    true
    |> get_complete_events_query()
    |> where([event], event.id in subquery(organizer_event_ids_query))
    |> maybe_filter_by_user_id(admin?, user_id)
    |> where(^filter_by_search_string(params))
    |> where(^filter_by_query_params(params))
    |> order_by(^order_by_query_params(params))
    |> Repo.paginate(params)
  end

  @doc """
  Gets all events filtered by seller organizer permissions (admin function).

  Only shows events if the seller is an Organizer and its Promoter's created_by_document_id
  has EventPermission to the event.
  """
  @spec list_all_events_by_seller(String.t()) :: [Event.t()]
  def list_all_events_by_seller(seller_id) do
    organizer_event_ids_query = get_organizer_event_ids_query(seller_id)

    true
    |> get_events_overview_query()
    |> where([event], event.id in subquery(organizer_event_ids_query))
    |> Repo.all()
  end

  @doc """
  Finds events by user document ID filtered by seller organizer permissions.

  Only shows events if the seller is an Organizer and its Promoter's created_by_document_id
  has EventPermission to the event.
  """
  @spec list_all_events_by_user_id_and_seller_id(String.t(), String.t()) :: [Event.t()]
  def list_all_events_by_user_id_and_seller_id(user_id, seller_id) do
    user_field = if uuid?(user_id), do: :user_id, else: :user_document_id

    organizer_event_ids_query = get_organizer_event_ids_query(seller_id)

    user_event_query =
      from(ep in EventPermission,
        where: field(ep, ^user_field) == ^user_id,
        select: ep.event_id
      )

    true
    |> get_complete_events_query()
    |> where([event], event.id in subquery(organizer_event_ids_query))
    |> where([event], event.id in subquery(user_event_query))
    |> Repo.all()
  end

  @doc """
  One use code to add end the default end date (start date + 12 hours) to all open-end events without an end date.
  """
  @spec add_end_date_to_open_end_events() :: {:ok, any()} | {:error, any()}
  def add_end_date_to_open_end_events do
    open_end_events_without_end_date = Repo.all(from(e in Event, where: is_nil(e.end_date)))

    multi =
      Enum.reduce(open_end_events_without_end_date, Multi.new(), fn event, multi ->
        Multi.update(
          multi,
          "update_event_#{event.id}",
          Event.changeset(event, %{end_date: DateTime.add(event.start_date, 12, :hour)})
        )
      end)

    Repo.transaction(multi)
  end

  # generate sql query to select events with all details
  defp get_complete_events_query(is_visible, include_guestlists \\ false)

  defp get_complete_events_query(is_visible, false) do
    base = base_complete_event_preloads(is_visible)

    from(event in Event,
      as: :event,
      left_join: ec in EventCounter,
      on: ec.event_id == event.id,
      as: :event_counter,
      preload: [
        :donations,
        :artists,
        :permissions,
        :channel_configs,
        :platform_fees,
        event_counter: ec,
        entrance_areas: ^base.entrance_areas_query,
        fees: ^base.fee_query,
        ticket_categories: ^base.ticket_category_query,
        variants: ^base.variant_query,
        tracking_links: ^base.tracking_links_query,
        tracking_pixels: ^base.tracking_pixels_query,
        promoter: ^base.promoter_query,
        venue: ^base.venue_query
      ],
      where: is_nil(event.deleted_at)
    )
  end

  defp get_complete_events_query(is_visible, true) do
    base = base_complete_event_preloads(is_visible)
    guestlist_query = Invitation.get_reportable_guestlists_query()

    from(event in Event,
      left_join: ec in EventCounter,
      on: ec.event_id == event.id,
      as: :event_counter,
      preload: [
        :donations,
        :artists,
        :permissions,
        :channel_configs,
        :platform_fees,
        event_counter: ec,
        entrance_areas: ^base.entrance_areas_query,
        fees: ^base.fee_query,
        guestlists: ^guestlist_query,
        ticket_categories: ^base.ticket_category_query,
        variants: ^base.variant_query,
        tracking_links: ^base.tracking_links_query,
        tracking_pixels: ^base.tracking_pixels_query,
        promoter: ^base.promoter_query,
        venue: ^base.venue_query
      ],
      where: is_nil(event.deleted_at)
    )
  end

  defp base_complete_event_preloads(is_visible) do
    fee_query = generate_fee_query()

    ticket_category_query =
      {generate_ticket_category_query(),
       [
         entrance_area_ticket_categories: [
           entrance_area: generate_entrance_area_query()
         ]
       ]}

    venue_query = generate_venue_query()

    promoter_query =
      from(p in Promoter, where: is_nil(p.deleted_at), preload: [:seatsio_workspace_keys, address: ^address_query()])

    tracking_pixels_query =
      from(tp in TrackingPixel,
        where: is_nil(tp.deleted_at),
        preload: [:tracking_pixel_credentials]
      )

    variant_query = generate_variant_query(is_visible)

    tracking_links_query = from(tl in TrackingLink, where: is_nil(tl.deleted_at))
    entrance_areas_query = from(ea in EntranceArea, where: is_nil(ea.deleted_at))

    %{
      fee_query: fee_query,
      ticket_category_query: ticket_category_query,
      venue_query: venue_query,
      promoter_query: promoter_query,
      tracking_pixels_query: tracking_pixels_query,
      variant_query: variant_query,
      tracking_links_query: tracking_links_query,
      entrance_areas_query: entrance_areas_query
    }
  end

  defp get_events_overview_query(is_visible) do
    fee_query = generate_fee_query()

    venue_query = generate_venue_query()

    promoter_query =
      from(p in Promoter, where: is_nil(p.deleted_at), preload: [:seatsio_workspace_keys, address: ^address_query()])

    variant_query = generate_variant_query(is_visible)

    from(event in Event,
      as: :event,
      left_join: ec in EventCounter,
      on: ec.event_id == event.id,
      as: :event_counter,
      preload: [
        :donations,
        :artists,
        :permissions,
        :channel_configs,
        :platform_fees,
        event_counter: ec,
        fees: ^fee_query,
        variants: ^variant_query,
        promoter: ^promoter_query,
        venue: ^venue_query
      ],
      where: is_nil(event.deleted_at)
    )
  end

  defp generate_fee_query do
    from(fee in Fee, where: is_nil(fee.deleted_at))
  end

  defp generate_ticket_category_query do
    from(ticket_category in TicketCategory, where: is_nil(ticket_category.deleted_at))
  end

  defp generate_entrance_area_query do
    from(entrance_area in EntranceArea, where: is_nil(entrance_area.deleted_at))
  end

  defp generate_venue_query do
    from(venue in Venue, preload: [address: ^address_query()])
  end

  defp address_query do
    from(address in Address, preload: [:country])
  end

  defp generate_variant_query(nil) do
    from(variant in Variant,
      where: is_nil(variant.deleted_at),
      preload: [
        :availability,
        :mlpm,
        :variant_counter,
        sales_channel: [:channel_config],
        ticket_category: [entrance_area_ticket_categories: [:entrance_area]]
      ]
    )
  end

  defp generate_variant_query(is_visible) do
    from(variant in Variant,
      where: is_nil(variant.deleted_at) and variant.is_visible == ^is_visible,
      preload: [
        :availability,
        :mlpm,
        :variant_counter,
        sales_channel: [:channel_config],
        ticket_category: [entrance_area_ticket_categories: [:entrance_area]]
      ]
    )
  end

  defp generate_public_variant_query do
    from(variant in Variant,
      inner_join: ticket_category in assoc(variant, :ticket_category),
      on: variant.ticket_category_id == ticket_category.id,
      preload: [:variant_counter, :availability, :ticket_category, sales_channel: [:channel_config]],
      where: is_nil(variant.deleted_at),
      where: variant.is_visible == true,
      where: ticket_category.is_visible == true,
      where: is_nil(ticket_category.deleted_at),
      order_by: [variant.order_no, desc: variant.unit_price]
    )
  end

  defp insert_artists(multi, nil), do: multi

  defp insert_artists(multi, artists) do
    artists
    |> prepare_attrs(Artist)
    |> Enum.reduce({multi, 0}, fn artist, {multi, count} ->
      multi
      |> Multi.insert({:artist, artist["source_id"]}, fn %{event: event} ->
        Artist.changeset(%Artist{}, Map.put(artist, "event_id", event.id))
      end)
      |> then(&{&1, count + 1})
    end)
    |> elem(0)
  end

  defp insert_fees(multi, nil), do: multi

  defp insert_fees(multi, fees) do
    # for backward compatibility with old events api we have to multiply the fee by 100 to get the correct value
    fees = Enum.map(fees, fn fee -> Map.put(fee, "unitPrice", round(fee["unitPrice"] * 100)) end)

    fees
    |> prepare_attrs(Fee)
    |> Enum.reduce({multi, 0}, fn fee, {multi, count} ->
      multi
      |> Multi.insert({:fee, count}, fn %{event: event} ->
        Fee.changeset(%Fee{}, Map.put(fee, "event_id", event.id))
      end)
      |> then(&{&1, count + 1})
    end)
    |> elem(0)
  end

  defp prepare_attrs(params, schema) do
    Enum.map(params, fn item -> schema.prepare_attrs(item) end)
  end

  defp maybe_update_balance_account(multi, event, attrs) do
    event_changeset = Event.update_changeset(event, Event.prepare_update_attrs(attrs, event))

    if not is_nil(get_change(event_changeset, :title)) || not is_nil(get_change(event_changeset, :start_date)) do
      Multi.run(multi, :update_balance_account, fn _repo, %{event: updated_event} = _changes ->
        update_event_balance_account(updated_event)
      end)
    else
      multi
    end
  end

  defp maybe_transfer_seating_plan(%Event{promoter_id: promoter_id} = event) do
    with {_, true} <- {:has_seating_plan, has_seating_plan?(event)},
         {_, %Promoter{} = promoter} <- {:get_promoter, Vendor.get_promoter!(promoter_id, [:seatsio_workspace_keys])},
         {_, {:ok, %{"key" => new_chart_key} = _chart}} <-
           {:create_seatsio_event, EventsSeatsio.create_seatsio_event(%{event | is_draft: false}, promoter)},
         {_, {:ok, updated_event}} <-
           {:update_chart_key, Repo.update(Event.changeset(event, %{chart_key: new_chart_key}))} do
      {:ok, updated_event}
    else
      # No seating plan to transfer
      {:has_seating_plan, false} ->
        {:ok, event}

      {:get_promoter, nil} ->
        Logger.error("Failed to transfer seating plan, promoter #{inspect(promoter_id)} not found")
        {:error, :promoter_not_found}

      {:create_seatsio_event, error} ->
        Logger.error("Failed to transfer seating plan, error #{inspect(error)}")
        {:error, error}

      {:update_chart_key, error} ->
        Logger.error("Failed to update event when transfering seating plan, error #{inspect(error)}")
        {:error, error}
    end
  end

  defp add_seats_and_chart_key_to_multi(multi, promoter) do
    Multi.update(multi, :seats_and_chart_key, fn %{event: event} ->
      case EventsSeatsio.create_seatsio_event(event, promoter) do
        {:ok, :no_chart_key} ->
          Event.changeset(event, %{})

        {:ok, %{"key" => chart_key}} ->
          # I'm not 100% sure yet if we want this. This would 'correctly' set the new chart key of\
          # the event after copying it to a new workspace.
          # However, we lose all relation to the old 'original' chart (except name)
          # I believe, in frontend we need to use the 'original' chart key from the template workspace
          Event.changeset(event, %{"chart_key" => chart_key})

        # Event.changeset(event, %{})

        error ->
          Logger.error("Failed to create seatsio event, error #{inspect(error)}")

          Ecto.Changeset.add_error(
            %Ecto.Changeset{data: event, valid?: false},
            :seatsio,
            "Failed to create seatsio event"
          )
      end
    end)
  end

  # Check if event has a seating plan configuration
  defp has_seating_plan?(%Event{chart_key: key}), do: !is_nil(key)

  defp maybe_import_chart_categories_into_variants(%Event{chart_key: nil}), do: {:ok, :no_chart_key}

  defp maybe_import_chart_categories_into_variants(%Event{id: event_id, chart_key: chart_key}) do
    with {_, {:ok, categories}} <- {:get_chart_categories, Charts.list_categories(chart_key)},
         {_, {:ok, reports}} <- {:get_chart_reports, ChartReports.summary_by_object_type(chart_key)},
         {_, transformed_categories} <-
           {:transform_categories, EventsSeatsio.transform_categories(categories, reports, event_id)},
         {_, {:ok, variants}} <- {:insert_variants, insert_variants(transformed_categories)} do
      {:ok, variants}
    else
      {:get_chart_categories, error} ->
        Logger.error("Failed to get chart categories for event_id=#{event_id}, error: #{inspect(error)}")
        error

      {:get_chart_reports, error} ->
        Logger.error("Failed to get chart reports for event_id=#{event_id}, error: #{inspect(error)}")
        error

      {:insert_variants, error} ->
        Logger.error("Failed to insert variants for event_id=#{event_id}, error: #{inspect(error)}")
        error
    end
  end

  defp maybe_update_artist(multi, nil, _event), do: multi

  defp maybe_update_artist(multi, artists, event) do
    query = from(a in Artist, where: a.event_id == ^event.id)

    multi
    |> Multi.delete_all(:artist, query)
    |> insert_artists(artists)
  end

  defp generate_public_event_query(condition) do
    address_query = from(address in Address, preload: [:country])
    venue_query = from(venue in Venue, preload: [address: ^address_query])
    variant_query = generate_public_variant_query()
    fee_query = generate_fee_query()

    tracking_pixels_query =
      from(tp in TrackingPixel,
        where: is_nil(tp.deleted_at),
        preload: [:tracking_pixel_credentials]
      )

    base_query =
      from(event in Event,
        preload: [
          :donations,
          :ticket_categories,
          :artists,
          fees: ^fee_query,
          variants: ^variant_query,
          venue: ^venue_query,
          tracking_pixels: ^tracking_pixels_query,
          promoter: [:seatsio_workspace_keys]
        ],
        where: is_nil(event.deleted_at)
      )

    case condition do
      [event_id: id] -> where(base_query, [event], event.id == ^id)
      [short_code: code] -> where(base_query, [event], event.short_code == ^code)
    end
  end

  defp do_publish_draft_event(%{id: event_id} = event) do
    Multi.new()
    |> purge_draft_data(event_id)
    |> set_draft_to_event(event)
    |> Repo.transaction()
  end

  defp purge_draft_data(multi, event_id) do
    multi
    |> Variant.build_get_all_multi(event_id)
    |> VariantCounter.build_delete_all_multi()
    |> TicketCategory.build_get_all_multi(event_id)
    |> TicketCategoryCounter.build_delete_all_multi()
    |> EventCounter.build_delete_all_multi(event_id)
    |> Voucher.build_delete_all_multi(event_id, :EVENT)
    |> VoucherCounter.build_delete_all_multi()
    |> Guestlist.build_delete_all_multi(event_id)
    |> GuestlistHistory.build_delete_all_multi()
    |> Invitation.build_delete_all_multi()
    |> InvitationLink.build_delete_all_multi()
    |> InvitationHistory.build_delete_all_multi()
    |> InvitationApprovalRequest.build_delete_all_multi()
    |> InvitationAttendee.build_delete_all_multi()
  end

  defp set_draft_to_event(multi, event) do
    multi
    |> Multi.update(:set_draft_attrs, Event.publish_draft_changeset(event, %{is_draft: false, closed_at: nil}))
    |> Multi.update(:set_approved, Event.approve_changeset(event, %{is_approved: true}))
    |> Multi.update(:set_public, Event.publish_changeset(event, %{is_visible: true}))
  end

  defp create_balance_account_for_event(%Promoter{account_holder_id: account_holder_id} = _promoter, event) do
    payload = %{
      account_holder_id: account_holder_id,
      reference: event.id,
      description: "#{event.title} - #{event.start_date}"
    }

    BalancePlatform.create_balance_account(payload)
  end

  defp update_event_balance_account(
         %{id: event_id, title: title, start_date: start_date, balance_account_id: balance_account_id} = _event
       ) do
    case BalancePlatform.update_balance_account(balance_account_id, %{description: "#{title} - #{start_date}"}) do
      {:error, msg} ->
        Logger.critical("Failed to update balance account for event: #{inspect(event_id)} with error: #{inspect(msg)}")
        {:error, msg}

      {:ok, balance_account} ->
        {:ok, balance_account}
    end
  end

  defp add_slug_update(event, attrs) do
    %Venue{name: venue_name} =
      case Map.get(attrs, "venueId") do
        nil -> event.venue
        venue_id -> Location.get_venue(venue_id)
      end

    # We need all three values to generate the slug, but not all three values are necessarily updated.
    # in case a value it is not updated, we put the existing information in its place, hence `put_new`
    attrs
    |> Map.put_new("startDate", event.start_date)
    |> Map.put_new("title", event.title)
    |> Map.put("venueName", venue_name)
    |> get_slug()
  end

  defp insert_variants(transformed_categories) do
    transformed_categories
    |> Enum.reduce([], fn category, acc ->
      case Variant.create_variant(category) do
        {:ok, variant} -> [variant | acc]
        error -> error
      end
    end)
    |> then(&{:ok, &1})
  end

  defp build_preload_queries(preloads) do
    Enum.map(preloads, &preload_query/1)
  end

  defp preload_query(:channel_configs),
    do: {:channel_configs, from(config in ChannelConfig, where: is_nil(config.deleted_at))}

  defp preload_query(:tracking_pixels),
    do: {:tracking_pixels, from(pixel in TrackingPixel, where: is_nil(pixel.deleted_at))}

  defp preload_query(:fees), do: {:fees, from(fee in Fee, where: is_nil(fee.deleted_at))}

  defp preload_query(:platform_fees), do: {:platform_fees, from(fee in PlatformFee, where: is_nil(fee.deleted_at))}

  defp preload_query(:venue), do: {:venue, from(venue in Venue, preload: [address: ^address_query()])}

  defp preload_query(preload), do: preload

  defp order_by_query_params(%{order: order_dir, sort: :title} = _params),
    do: [{order_dir, dynamic([event], event.title)}]

  defp order_by_query_params(%{order: order_dir, sort: sort_param} = _params)
       when sort_param in [:startDate, :start_date],
       do: [{order_dir, dynamic([event], event.start_date)}, {:asc, dynamic([event], event.id)}]

  defp order_by_query_params(%{order: order_dir, sort: sort_param} = _params)
       when sort_param in [:soldTickets, :sold_tickets],
       do: [
         {order_dir, dynamic([event, event_counter: ec], coalesce(ec.sold_tickets, 0))},
         {:asc, dynamic([event], event.id)}
       ]

  defp order_by_query_params(%{order: order_dir, sort: sort_param} = _params)
       when sort_param in [:soldExtras, :sold_extras],
       do: [
         {order_dir, dynamic([event, event_counter: ec], coalesce(ec.sold_extras, 0))},
         {:asc, dynamic([event], event.id)}
       ]

  defp order_by_query_params(%{order: order_dir, sort: sort_param} = _params)
       when sort_param in [:totalSales, :total_sales],
       do: [
         {order_dir, dynamic([event, event_counter: ec], coalesce(ec.total_sales, 0))},
         {:asc, dynamic([event], event.id)}
       ]

  defp order_by_query_params(%{order: order_dir, sort: :popular} = _params),
    do: [
      {order_dir, dynamic([event, event_counter: ec], coalesce(ec.total_sales, 0))},
      {:asc, dynamic([event], event.id)}
    ]

  defp order_by_query_params(%{order: order_dir, sort: sort_param} = _params)
       when sort_param in [:totalCheckedIn, :total_checked_in],
       do: [
         {order_dir, dynamic([event, event_counter: ec], coalesce(ec.total_checked_in, 0))},
         {:asc, dynamic([event], event.id)}
       ]

  defp order_by_query_params(_params),
    do: [{:asc, dynamic([event], event.start_date)}, {:asc, dynamic([event], event.id)}]

  defp filter_by_query_params(%{status: :all} = _params) do
    dynamic([event], true)
  end

  defp filter_by_query_params(%{status: :draft} = _params) do
    dynamic([event], event.is_draft == true)
  end

  # returns all upcoming and ongoing events for the landing page in the web client
  defp filter_by_query_params(%{status: :upcoming} = _params) do
    now = DateTime.utc_now()
    twelve_hours_before_now = DateTime.add(now, -12, :hour)

    dynamic(
      [event],
      is_nil(event.closed_at) and event.is_draft == false and
        ((is_nil(event.end_date) and event.start_date >= ^twelve_hours_before_now) or event.end_date >= ^now)
    )
  end

  # returns all upcoming and ongoing events for event list in the admission app
  defp filter_by_query_params(%{status: :ongoing} = _params) do
    now = DateTime.utc_now()
    twelve_hours_before_now = DateTime.add(now, -12, :hour)

    dynamic(
      [event],
      is_nil(event.closed_at) and event.is_draft == false and
        ((is_nil(event.end_date) and event.start_date >= ^twelve_hours_before_now) or event.end_date >= ^now)
    )
  end

  defp filter_by_query_params(%{status: :completed} = _params) do
    now = DateTime.utc_now()
    twelve_hours_before_now = DateTime.add(now, -12, :hour)

    dynamic(
      [event],
      event.is_draft == false and
        (not is_nil(event.closed_at) or
           ((is_nil(event.end_date) and event.start_date < ^twelve_hours_before_now) or event.end_date < ^now))
    )
  end

  defp filter_by_query_params(_params) do
    filter_by_query_params(%{status: :upcoming})
  end

  defp filter_by_search_string(%{search: search_string} = _params) do
    like_search_term = "%#{search_string}%"
    dynamic([event], ilike(event.title, ^like_search_term) or ilike(event.subtitle, ^like_search_term))
  end

  defp filter_by_search_string(_params), do: dynamic([event], true)

  defp maybe_filter_by_user_id(query, true, _user_id), do: query

  defp maybe_filter_by_user_id(query, _admin?, user_id) do
    user_field = if uuid?(user_id), do: :user_id, else: :user_document_id

    user_event_query =
      from(ep in EventPermission,
        where:
          field(ep, ^user_field) == ^user_id and
            (^"EVENT_ADMIN" in ep.role or ^"SECURITY" in ep.role),
        select: ep.event_id
      )

    where(query, [event], event.id in subquery(user_event_query))
  end

  defp filter_ended_events(query) do
    now = DateTime.utc_now()
    twelve_hours_before_now = DateTime.add(now, -12, :hour)

    where(
      query,
      [event: event],
      ^now < event.end_date or (is_nil(event.end_date) and ^twelve_hours_before_now < event.start_date)
    )
  end

  # Helper function for date range filtering in seller events
  defp filter_by_date_range(query, %{start_date_from: nil, start_date_to: nil}), do: query

  defp filter_by_date_range(query, %{start_date_from: from_date, start_date_to: nil}) when not is_nil(from_date) do
    where(query, [event], event.start_date >= ^from_date)
  end

  defp filter_by_date_range(query, %{start_date_from: nil, start_date_to: to_date}) when not is_nil(to_date) do
    where(query, [event], event.start_date <= ^to_date)
  end

  defp filter_by_date_range(query, %{start_date_from: from_date, start_date_to: to_date})
       when not is_nil(from_date) and not is_nil(to_date) do
    where(query, [event], event.start_date >= ^from_date and event.start_date <= ^to_date)
  end

  # Helper function to filter by venue city for seller events
  defp filter_seller_events_by_venue_city(query, nil), do: query

  defp filter_seller_events_by_venue_city(query, venue_city) do
    query
    |> join(:inner, [event], venue in Venue, as: :venue, on: event.venue_id == venue.id)
    |> join(:inner, [event, venue: venue], address in Address, as: :address, on: venue.address_id == address.id)
    |> where([event, venue: _venue, address: address], address.locality == ^venue_city)
  end

  defp uuid?(str) do
    case Ecto.UUID.dump(str) do
      {:ok, _} -> true
      :error -> false
    end
  end

  defp add_event_sales_permissions(multi, promoter_id) do
    seller_ids = Sellers.get_seller_ids_with_promoter_permission(promoter_id)

    seller_ids
    |> Enum.with_index()
    |> Enum.reduce(multi, fn {seller_id, index}, acc_multi ->
      Multi.insert(acc_multi, {:event_sales_permission, index}, fn %{event: event} ->
        SalesPermission.changeset(%SalesPermission{}, %{
          type: :EVENT,
          type_id: event.id,
          seller_id: seller_id
        })
      end)
    end)
  end

  defp base_organizer_permission_query(seller_id) do
    from(s in Seller,
      join: o in Organizer,
      on: s.type_id == o.id,
      as: :organizer,
      join: p in Promoter,
      on: o.promoter_id == p.id,
      as: :promoter,
      join: ep in EventPermission,
      on:
        (not is_nil(ep.user_document_id) and ep.user_document_id == p.created_by_document_id) or
          (not is_nil(ep.user_id) and ep.user_id == p.created_by),
      as: :event_permission,
      where: ep.inherited_from_seller? == false,
      where: s.id == ^seller_id,
      where: s.type == :ORGANIZER,
      where: is_nil(ep.deleted_at),
      where: is_nil(s.deleted_at),
      where: is_nil(o.deleted_at)
    )
  end

  # Helper function to get event IDs for organizer seller
  # Only returns events where the seller is an ORGANIZER and its promoter's
  # created_by_document_id has EventPermission (EVENT_ADMIN or SECURITY role)
  # Base query that both functions can use
  defp get_organizer_event_ids_query(seller_id) do
    seller_id
    |> base_organizer_permission_query()
    |> select([event_permission: ep], ep.event_id)
  end

  # Helper function to check if a seller has access to a specific event
  # Only returns true if the seller is an ORGANIZER and its promoter's
  # created_by_document_id has EventPermission to the event
  defp has_seller_access_to_event?(seller_id, event_id) do
    seller_id
    |> base_organizer_permission_query()
    |> where([event_permission: ep], ep.event_id == ^event_id)
    |> select([], 1)
    |> Repo.exists?()
  end

  defp admin_user?(roles) do
    "ADMIN" in roles
  end

  defp seller_and_user_access?(event_id, permission, user_id, seller_id) do
    event_permission = get_event_permission(user_id, event_id)
    user_has_permission? = has_user_permission?(event_permission, permission)
    seller_has_access? = has_seller_access_to_event?(seller_id, event_id)
    access? = seller_has_access? && user_has_permission?

    if access? do
      Logger.debug(
        "User #{inspect(user_id)} has seller organizer access and user permission for event #{inspect(event_id)}"
      )
    else
      Logger.warning(
        "User #{inspect(user_id)} lacks required access to event #{inspect(event_id)} " <>
          "with permission #{inspect(permission)}. " <>
          "Seller access: #{seller_has_access?}, User permission: #{user_has_permission?}"
      )
    end

    access?
  end

  defp user_access?(event_id, permission, user_id) do
    event_permission = get_event_permission(user_id, event_id)
    access? = has_user_permission?(event_permission, permission)

    if access? do
      Logger.debug("User #{inspect(user_id)} has user permission for event #{inspect(event_id)}")
    else
      Logger.warning(
        "User #{inspect(user_id)} is not authorized to access event #{inspect(event_id)} " <>
          "with permission #{inspect(permission)}"
      )
    end

    access?
  end

  defp has_user_permission?(event_permission, permission) do
    find_user_permission(event_permission, permission) != nil
  end

  defp validate_event_start_date_for_publication(%Event{start_date: nil}) do
    {:error, :event_start_date_missing}
  end

  defp validate_event_start_date_for_publication(%Event{start_date: start_date}) do
    now = DateTime.utc_now()

    if DateTime.after?(start_date, now) do
      :ok
    else
      {:error, :event_start_date_not_in_future}
    end
  end

  defp validate_event_admission_date_for_publication(%Event{admission_date: nil}) do
    :ok
  end

  defp validate_event_admission_date_for_publication(%Event{admission_date: admission_date}) do
    now = DateTime.utc_now()

    if DateTime.after?(admission_date, now) do
      :ok
    else
      {:error, :event_admission_date_not_in_future}
    end
  end

  defp fetch_promoter(user_id, nil) do
    case Vendor.get_promoter_by_creator(user_id, [:seatsio_workspace_keys]) do
      %Promoter{} = promoter ->
        {:ok, promoter}

      _ ->
        Logger.error("Promoter not found for user #{user_id} when creating event")
        {:error, :promoter_not_found}
    end
  end

  defp fetch_promoter(user_id, seller_id) do
    case Vendor.get_promoter_by_seller_id(seller_id, [:seatsio_workspace_keys]) do
      %Promoter{} = promoter ->
        {:ok, promoter}

      _ ->
        Logger.error("Promoter not found for seller #{seller_id} when creating event for user #{user_id}")
        {:error, :promoter_not_found}
    end
  end
end
