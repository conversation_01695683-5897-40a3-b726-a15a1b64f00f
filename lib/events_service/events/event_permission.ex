defmodule EventsService.Events.EventPermission do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query, warn: false

  alias EventsService.Events.EventPermission
  alias EventsService.Repo

  require Logger

  @roles [:EVENT_ADMIN, :SECURITY]

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  schema "event_permissions" do
    belongs_to :event, EventsService.Events.Event

    field :deleted_at, :utc_datetime
    field :inherited_from_seller?, :boolean, default: false, source: :is_inherited_from_seller
    field :is_system_permission, :boolean, default: false
    field :role, {:array, Ecto.Enum}, values: @roles
    field :user_document_id, :string
    field :user_id, Ecto.UUID
    timestamps()
  end

  @type t :: %EventPermission{
          __meta__: Ecto.Schema.Metadata.t(),
          id: binary(),
          inherited_from_seller?: boolean(),
          role: atom(),
          event: struct(),
          user_document_id: String.t(),
          user_id: binary(),
          is_system_permission: boolean(),
          deleted_at: DateTime.t(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @doc false
  def changeset(event_permission, attrs) do
    event_permission
    |> cast(attrs, [
      :event_id,
      :user_id,
      :user_document_id,
      :role,
      :deleted_at,
      :inherited_from_seller?,
      :is_system_permission
    ])
    |> validate_required([:event_id, :role])
    |> validate_at_least_one_user_identifier()
    |> validate_roles_inclusion(:role)
    |> maybe_set_system_permission_flag()
  end

  def update_changeset(event_permission, attrs) do
    event_permission
    |> cast(attrs, [:role, :user_document_id, :user_id, :inherited_from_seller?, :is_system_permission])
    |> validate_roles_inclusion(:role)
    |> maybe_set_system_permission_flag()
  end

  def transform_roles(roles) do
    Enum.map(roles, fn role -> String.to_atom(role) end)
  end

  def get_event_permission(id, preloads \\ []) do
    query =
      from(ep in EventPermission,
        where: ep.id == ^id,
        preload: ^preloads
      )

    Repo.one(query)
  end

  @spec create_event_permission(map(), binary(), binary()) :: {:ok, EventPermission.t()} | {:error, Ecto.Changeset.t()}
  def create_event_permission(params, user_id, event_id) do
    user_field = if uuid?(user_id), do: :user_id, else: :user_document_id

    query =
      from(ep in EventPermission,
        where: field(ep, ^user_field) == ^user_id,
        where: ep.event_id == ^event_id
      )

    attrs = %{
      :role => params["roles"],
      :event_id => params["id"],
      user_field => user_id
    }

    event_permission =
      case Repo.one(query) do
        nil -> %EventPermission{}
        event_permission -> event_permission
      end

    event_permission
    |> changeset(attrs)
    |> Repo.insert_or_update()
  end

  @spec update_event_permission(
          event_permission :: EventPermission.t(),
          user_id :: binary(),
          roles :: [String.t()]
        ) ::
          {:ok, EventPermission.t()} | {:error, any()}
  def update_event_permission(event_permission, user_id, roles) do
    user_field = if uuid?(user_id), do: :user_id, else: :user_document_id

    event_permission
    |> update_changeset(%{
      :role => roles,
      user_field => user_id
    })
    |> Repo.update()
  end

  def delete_event_permission(event_permission), do: Repo.delete(event_permission)

  def roles, do: @roles

  defp validate_roles_inclusion(changeset, field) do
    validate_change(changeset, field, fn _, roles ->
      if Enum.all?(roles, &Enum.member?(@roles, &1)) do
        []
      else
        [{field, "Invalid role"}]
      end
    end)
  end

  defp validate_at_least_one_user_identifier(changeset) do
    user_id = get_field(changeset, :user_id)
    user_document_id = get_field(changeset, :user_document_id)

    if is_nil(user_id) && is_nil(user_document_id) do
      add_error(changeset, :user_identifiers, "either user_id or user_document_id must be present")
    else
      changeset
    end
  end

  defp uuid?(str) do
    case Ecto.UUID.cast(str) do
      {:ok, _} -> true
      :error -> false
    end
  end

  defp maybe_set_system_permission_flag(changeset) do
    user_document_id = get_field(changeset, :user_document_id)
    roles = get_field(changeset, :role) || []

    put_change(changeset, :is_system_permission, security_user?(user_document_id, roles))
  end

  defp security_user?(user_document_id, roles) when is_binary(user_document_id) do
    String.starts_with?(user_document_id, "sec_") and :SECURITY in roles
  end

  defp security_user?(_, _), do: false
end
