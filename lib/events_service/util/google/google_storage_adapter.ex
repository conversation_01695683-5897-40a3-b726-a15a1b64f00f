defmodule EventsService.Util.Google.GoogleStorageAdapter do
  @moduledoc false

  alias GoogleApi.Storage.V1.Api.Buckets
  alias GoogleApi.Storage.V1.Api.Objects
  alias GoogleApi.Storage.V1.Connection

  require Logger

  @spec upload(binary(), String.t(), String.t()) :: {:ok, map()} | {:error, any()}
  def upload(local_path, path, content_type) do
    metadata = %GoogleApi.Storage.V1.Model.Object{name: path, contentType: content_type}

    with {:ok, token} <- maybe_fetch_token(),
         storage_connection = get_connection(token),
         {:ok, bucket} <- fetch_bucket(storage_connection, nil, true) do
      Objects.storage_objects_insert_simple(
        storage_connection,
        bucket.id,
        "multipart",
        metadata,
        local_path
      )
    end
  end

  @spec get(String.t(), String.t(), boolean()) :: {:ok, binary()} | {:error, any()}
  def get(path, bucket_name, from_public) do
    with {_, {:ok, token}} <- {:get_goth, maybe_fetch_token()},
         storage_connection = get_connection(token),
         {_, {:ok, bucket}} <- {:fetch_bucket, fetch_bucket(storage_connection, bucket_name, from_public)},
         {_, {:ok, storage_object}} <- {:get_objects, Objects.storage_objects_get(storage_connection, bucket.id, path)} do
      download_via_media_link(storage_object.mediaLink, token)
    else
      {:get_goth, error} ->
        Logger.error("Failed to fetch goth token with error: #{inspect(error)}")
        {:error, error}

      {:fetch_bucket, error} ->
        Logger.error("Failed to fetch bucket with error: #{inspect(error)}")
        {:error, error}

      {:get_objects, error} ->
        Logger.error("Failed to fetch object with error: #{inspect(error)}")
        {:error, error}
    end
  end

  @spec fetch_bucket(Tesla.Client.t(), String.t(), boolean()) :: :not_found | {:ok, any()}
  def fetch_bucket(storage_connection, nil, true),
    do:
      fetch_bucket(
        storage_connection,
        "#{Application.get_env(:events_service, :gcloud_project, "stdts-prod")}-cdn-bucket"
      )

  def fetch_bucket(storage_connection, nil, false),
    do:
      fetch_bucket(
        storage_connection,
        "#{Application.get_env(:events_service, :gcloud_project, "stdts-prod")}-cdn-bucket-private"
      )

  def fetch_bucket(storage_connection, bucket_name, _), do: fetch_bucket(storage_connection, bucket_name)

  @spec fetch_bucket(Tesla.Client.t(), String.t()) :: :not_found | {:ok, any()}
  def fetch_bucket(storage_connection, bucket_name) do
    result =
      storage_connection
      |> list_buckets()
      |> Enum.filter(fn %{name: name} -> name == bucket_name end)

    case result do
      [one | _xs] -> {:ok, one}
      _other -> :not_found
    end
  end

  @spec list_buckets(Tesla.Client.t()) :: nil | [map()] | {:error, Tesla.Env.t()}
  def list_buckets(storage_connection) do
    with {:ok, response} <- Buckets.storage_buckets_list(storage_connection, get_project_id()) do
      response.items
    end
  end

  @spec get_project_id() :: String.t()
  def get_project_id do
    Application.get_env(:events_service, :gcloud)
  end

  defp download_via_media_link(media_link, token) do
    headers = [{"Authorization", "Bearer #{token}"}]

    case Tesla.get(media_link, headers: headers) do
      {:ok, %{status: 200, body: body}} ->
        {:ok, body}

      {:ok, %{status: status, body: body}} ->
        Logger.error("Failed to fetch asset #{inspect(media_link)} with status #{status} and error: #{inspect(body)}")
        {:error, body}

      {:error, reason} ->
        Logger.error("Failed to fetch asset #{inspect(media_link)} with error: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp get_connection(token) do
    if Enum.member?([:local, :minikube], Application.get_env(:events_service, :environment)) do
      Connection.new()
    else
      Connection.new(token)
    end
  end

  defp maybe_fetch_token do
    if Enum.member?([:local, :minikube], Application.get_env(:events_service, :environment)) do
      {:ok, nil}
    else
      case Goth.fetch(EventsService.Goth) do
        {:ok, %{token: token}} -> {:ok, token}
        error -> error
      end
    end
  end
end
