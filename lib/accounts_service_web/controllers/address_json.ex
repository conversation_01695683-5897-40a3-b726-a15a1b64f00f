defmodule AccountsServiceWeb.AddressJSON do
  @moduledoc false

  @spec index(%{address_page: map()}) :: map()
  def index(%{address_page: %{entries: addresses} = page}) do
    %{
      data: for(address <- addresses, do: address_data(address)),
      pageNumber: page.page_number,
      pageSize: page.page_size,
      totalEntries: page.total_entries,
      totalPages: page.total_pages
    }
  end

  @spec show_address(%{address: map() | nil}) :: map() | nil
  def show_address(%{address: nil}), do: nil

  def show_address(%{address: address}) do
    address_data(address)
  end

  defp address_data(address) do
    %{
      country: get_country_name(Map.get(address, :country)),
      locality: Map.get(address, :locality),
      postalCode: Map.get(address, :postal_code),
      region: Map.get(address, :region),
      streetAddress: Map.get(address, :street_address),
      # optinal fields, not part of the openID standard fields
      company: Map.get(address, :company),
      countryIso: Map.get(address, :country_iso),
      id: Map.get(address, :id)
    }
  end

  defp get_country_name(%{country: country}), do: country
  defp get_country_name(_country), do: nil
end
