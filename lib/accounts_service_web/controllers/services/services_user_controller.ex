defmodule AccountsServiceWeb.Services.ServicesUserController do
  use AccountsServiceWeb, :controller

  alias AccountsService.CasdoorUsers
  alias AccountsService.Firebase
  alias AccountsService.Firebase.FirebaseAdmin
  alias AccountsService.Users
  alias AccountsService.Users.User

  require Logger

  action_fallback AccountsServiceWeb.FallbackController

  def userinfo(conn, params) do
    Logger.debug("/accounts/api/:id/userinfo called for user: #{params["id"] || params["email"]}")

    params =
      case params do
        %{"id" => user_document_id} ->
          # Decode the URL-encoded ID to handle format like 'organization/name'
          decoded_id = URI.decode_www_form(user_document_id)
          Logger.debug("Decoded user ID: #{decoded_id}")

          # Check if the decoded_id is a UUID
          case Ecto.UUID.cast(decoded_id) do
            {:ok, _result} -> [id: decoded_id]
            _ -> [external_id: decoded_id]
          end

        %{"email" => email} ->
          [email: email]
      end

    with {_, [api_token | _]} <- {:header, get_req_header(conn, "x-api-token")},
         {_, {:ok, _claim}} <- {:access, ExServiceClient.Token.verify_and_validate(api_token)},
         {_, %{id: user_id} = user} <- {:user, Users.get_by(params)} do
      user_profile = Users.get_user_profile_by_user_id(user_id)

      user_settings =
        case user_profile && Users.get_user_settings_by_user_profile_id(user_profile.id) do
          nil ->
            %{receive_first_party_emails: false, receive_third_party_emails: false}

          user_settings ->
            user_settings
        end

      conn
      |> put_status(:ok)
      |> put_resp_header("Cache-Control", "private")
      |> put_view(AccountsServiceWeb.UserJSON)
      |> render(:userinfo,
        userinfo: %{user: user, user_profile: user_profile, user_settings: user_settings}
      )
    else
      {:header, _} ->
        conn
        |> put_status(:unauthorized)
        |> put_resp_header("Cache-Control", "no-cache, no-store, max-age=0, must-revalidate")
        |> text("Missing x-api-token header")

      {:access, {:error, _}} ->
        conn
        |> put_status(:unauthorized)
        |> put_resp_header("Cache-Control", "no-cache, no-store, max-age=0, must-revalidate")
        |> text("Unauthorized")

      {:user, _} ->
        conn
        |> put_status(:internal_server_error)
        |> put_resp_header("Cache-Control", "no-cache, no-store, max-age=0, must-revalidate")
        |> text("User not found for token")
    end
  end

  def create_security_user(%{private: %{experimental: true}} = conn, _params) do
    Logger.debug("Creating security user in casdoor")

    with {_, [api_token | _]} <-
           {:header, get_req_header(conn, "x-api-token")},
         {_, {:ok, _claim}} <-
           {:access, ExServiceClient.Token.verify_and_validate(api_token)},
         {_, {:ok, %User{id: user_id}}} <-
           {:create_user, CasdoorUsers.create_security_user()} do
      conn
      |> put_status(:ok)
      |> json(%{id: user_id})
    else
      {:header, _} ->
        conn
        |> put_status(:unauthorized)
        |> json(%{error_code: :unauthorized, message: "No API token provided."})

      {:access, _} ->
        conn
        |> put_status(:unauthorized)
        |> json(%{error_code: :unauthorized, message: "Failed to authenticate service."})

      {:create_user, {:error, res}} ->
        Logger.error("Error creating user in Casdoor: #{inspect(res)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{error_code: :create_security_user, message: "Failed to create security user."})
    end
  end

  def create_security_user(conn, params) do
    Logger.debug("Creating security user")

    external_id = params["external_id"]

    with {_, [api_token | _]} <-
           {:header, get_req_header(conn, "x-api-token")},
         {_, {:ok, _claim}} <-
           {:access, ExServiceClient.Token.verify_and_validate(api_token)},
         {_, {:ok, %{"localId" => local_id}}} <-
           {:user_fs, FirebaseAdmin.create_security_user(external_id)},
         {_, {:ok, _}} <-
           {:user_claims, FirebaseAdmin.set_custom_user_claims(local_id, %{"roles" => ["SECURITY"]})},
         {_, {:ok, _user}} <-
           {:user_db, Users.create_security_user(%{"external_id" => local_id})} do
      conn
      |> put_status(:ok)
      |> json(%{id: local_id})
    else
      {:header, _} ->
        conn
        |> put_status(:unauthorized)
        |> text("Missing x-api-token header")

      {:access, {:error, _}} ->
        conn
        |> put_status(:unauthorized)
        |> text("Unauthorized")

      {:user_fs, {:error, res}} ->
        Logger.error("Error creating user in Firebase: #{inspect(res)}")

        conn
        |> put_status(:internal_server_error)
        |> text("Error creating user in Firebase")

      {:user_claims, {:error, res}} ->
        Logger.error("Error setting user claims in Firebase: #{inspect(res)}")

        conn
        |> put_status(:internal_server_error)
        |> text("Error setting user claims in Firebase")

      {:user_db, {:error, res}} ->
        Logger.error("Error creating user in database: #{inspect(res)}")

        conn
        |> put_status(:internal_server_error)
        |> text("Error creating user in database")
    end
  end

  def update_claims(conn, %{"id" => user_id, "roles" => roles} = _params) do
    Logger.debug("Setting user claims for user: #{user_id}")

    roles_to_add = roles["add"] || []
    roles_to_remove = roles["remove"] || []

    with {_, [api_token | _]} <-
           {:header, get_req_header(conn, "x-api-token")},
         {_, {:ok, _claim}} <-
           {:access, ExServiceClient.Token.verify_and_validate(api_token)},
         {_, {:ok, _}} <-
           {:claims, Firebase.update_user_roles(user_id, roles_to_add, roles_to_remove)} do
      conn
      |> put_status(:no_content)
      |> text("")
    else
      {:header, _} ->
        conn
        |> put_status(:unauthorized)
        |> text("Missing x-api-token header")

      {:access, {:error, _}} ->
        conn
        |> put_status(:unauthorized)
        |> text("Unauthorized")

      {:claims, {:error, _}} ->
        conn
        |> put_status(:internal_server_error)
        |> text("Error setting user claims in Firebase")
    end
  end
end
