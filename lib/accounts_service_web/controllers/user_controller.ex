defmodule AccountsServiceWeb.UserController do
  use AccountsServiceWeb, :controller
  use OpenApiSpex.ControllerSpecs
  use Params

  alias AccountsService.CasdoorUsers
  alias AccountsService.Firebase
  alias AccountsService.Firebase.FirebaseAdmin
  alias AccountsService.Firebase.FirebaseUser
  alias AccountsService.Params.CreateUser
  alias AccountsService.SellerSaga
  alias AccountsService.Token
  alias AccountsService.Users
  alias AccountsService.Users.User
  alias AccountsService.Users.UserProfile
  alias AccountsService.Util.Mailer
  alias AccountsServiceWeb.ChangesetJSON
  alias AccountsServiceWeb.Plugs.Authorize
  alias AccountsServiceWeb.Schemas.UserSchema.CasdoorUser
  alias AccountsServiceWeb.Schemas.UserSchema.CreateUserRequest
  alias AccountsServiceWeb.Schemas.UserSchema.CreateUserResponse
  alias AccountsServiceWeb.Schemas.UserSchema.FirebaseUserResponse
  alias AccountsServiceWeb.Schemas.UserSchema.ResetPasswordResponse
  alias AccountsServiceWeb.Schemas.UserSchema.SignInResponse
  alias AccountsServiceWeb.Schemas.UserSchema.SigninSecurityResponse
  alias AccountsServiceWeb.Schemas.UserSchema.UserResponse
  alias ExRBAC.Plug.EnsureAuthenticated
  alias OpenApiSpex.Parameter
  alias OpenApiSpex.Reference
  alias OpenApiSpex.Schema

  require Logger

  action_fallback AccountsServiceWeb.FallbackController

  plug :maybe_ensure_authenticated when action in [:create]
  plug :maybe_authorize, [rule: ["internal", "user", "write"]] when action in [:create]

  tags ["User"]

  operation :create,
    summary: "Create a user with email and password",
    parameters: [
      provider: [
        in: :query,
        description: "Authentication provider (optional, defaults to FIREBASE if not specified)",
        required: false,
        schema: %Schema{type: :string, enum: ["CASDOOR", "FIREBASE"], example: "CASDOOR"}
      ],
      type: [
        in: :query,
        description: "User type (optional, defaults to ORGANIZER if not specified)",
        required: false,
        schema: %Schema{
          type: :string,
          enum: ["ORGANIZER", "STORE", "POS", "SHOP", "PLATFORM"],
          example: "STORE",
          default: "ORGANIZER"
        }
      ]
    ],
    request_body: {"Create User Request", "application/json", CreateUserRequest},
    responses: %{
      :created => {
        "User Created Response",
        "application/json",
        %Schema{
          oneOf: [
            FirebaseUserResponse,
            CasdoorUser
          ],
          description: "Response depends on provider used - Firebase or Casdoor"
        }
      },
      :bad_request => %Reference{"$ref": "#/components/responses/400"},
      :unprocessable_entity => %Reference{"$ref": "#/components/responses/422"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/500"}
    }

  @spec create(Plug.Conn.t(), map()) :: Plug.Conn.t()
  def create(conn, %{"provider" => "CASDOOR"} = params) do
    with {_, %{valid?: true} = changeset} <-
           {:params, CreateUser.changeset(params)},
         {_, {:ok, _, %{updated_db_user: user}}} <-
           {:create_user, changeset |> Params.to_map() |> SellerSaga.create_seller_user()} do
      conn
      |> put_status(:created)
      |> render(:show, user: user)
    else
      {:params, changeset} ->
        Logger.debug("Invalid parameters were given when trying to create a casdoor user: #{inspect(changeset.errors)}")

        conn
        |> put_status(:bad_request)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)

      {:create_user, {:error, %Ecto.Changeset{} = changeset}} ->
        Logger.debug("Failed to create database casdoor user: #{inspect(changeset)}")

        conn
        |> put_status(:unprocessable_entity)
        |> put_view(ChangesetJSON)
        |> render(:error, changeset: changeset)

      {:create_user, {:error, error}} ->
        Logger.critical("Failed to create casdoor user: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          message: "Error while creating casdoor user",
          error_code: :casdoor_user_creation_failed
        })
    end
  end

  def create(conn, %{"email" => email, "password" => password} = _params) do
    with {:ok, %{"idToken" => id_token, "localId" => external_id}} <-
           FirebaseUser.create_user(email, password),
         {:ok, %{"users" => [user | _]}} <- FirebaseUser.get_user(id_token),
         {:ok, %{user: %User{id: user_id} = _local_user}} <-
           Users.create_user_account(%{
             email: email,
             external_id: external_id,
             provider: "FIREBASE",
             gender: "0"
           }),
         {:ok, _} <- Firebase.update_internal_user_id(external_id, user_id) do
      conn
      |> put_status(:created)
      |> json(user)
    else
      {:error, %{status: 400, body: %{"error" => %{"message" => "EMAIL_EXISTS"}}}} = error ->
        Logger.error("Error creating Firebase user, email exists: #{inspect(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> text("Der Benutzer konnte nicht erstellt werden, E-Mail existiert bereits.")

      {:error, %{status: 400}} = error ->
        Logger.error("Error creating Firebase user: #{inspect(error)}")

        conn
        |> put_status(:bad_request)
        |> text("Der Benutzer konnte nicht erstellt werden.")

      {:error, %{status: 422}} = error ->
        Logger.error("Error creating Firebase user: #{inspect(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> text("Der Benutzer konnte nicht erstellt werden.")

      {:error, _failed_operation, failed_value, _changes} ->
        Logger.error("Error creating user: #{inspect(failed_value)}")

        conn
        |> put_status(:unprocessable_entity)
        |> text("Der Benutzer konnte nicht erstellt werden.")

      {:error, error} ->
        Logger.error("Error creating Firebase user: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{error: "Could not create Firebase user."})

      {:ok, res} ->
        Logger.error("Unexpected response from Firebase: #{inspect(res)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{error: "User created, but could not be retrieved from Firebase."})
    end
  end

  operation :delete_user,
    summary: "Delete an user account",
    parameters: [
      %Parameter{
        in: :query,
        name: :delete_by,
        description:
          "Delete by method ('phone' to also delete users with whitelisted phone number, omit for default user deletion)",
        required: false,
        schema: %Schema{type: :string, enum: ["phone"]}
      }
    ],
    responses: %{
      :no_content => {"User deleted", "application/json", nil},
      :bad_request => %Reference{"$ref": "#/components/responses/400"},
      :unauthorized => %Reference{"$ref": "#/components/responses/401"},
      :forbidden => %Reference{"$ref": "#/components/responses/403"},
      :not_found => %Reference{"$ref": "#/components/responses/404"},
      :conflict => %Reference{"$ref": "#/components/responses/409"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/500"}
    }

  @spec delete_user(Plug.Conn.t(), map()) :: Plug.Conn.t()
  def delete_user(%{assigns: %{current_user_id: external_id}} = conn, %{"delete_by" => "phone"} = _params) do
    with {_, %User{} = user} <- {:find_user, Users.get_by(external_id: external_id)},
         {_, {:ok, %{"users" => [firebase_user | _]}}} <-
           {:get_firebase_user, FirebaseAdmin.get_user_by_id(external_id)},
         {_, phone_number} when not is_nil(phone_number) <-
           {:get_phone, Map.get(firebase_user, "phoneNumber")},
         {_, true} <-
           {:allowed_phone, normalise_phone(phone_number) in get_allowed_phone_numbers()},
         {_, {:ok, _response}} <-
           {:delete_firebase, FirebaseAdmin.delete_user(external_id)},
         {_, {:ok, _deleted_user}} <-
           {:mark_deleted, Users.mark_user_as_deleted(user)} do
      conn
      |> put_status(:no_content)
      |> text("")
    else
      {:find_user, nil} ->
        Logger.error("No local user found for external_id: #{external_id}")

        conn
        |> put_status(:not_found)
        |> json(%{
          message: "User not found in our database",
          error_code: :user_not_found
        })

      {:get_firebase_user, {:ok, %{"users" => []}}} ->
        Logger.error("No Firebase user found for external_id: #{external_id}")

        conn
        |> put_status(:not_found)
        |> json(%{
          message: "User not found in Firebase",
          error_code: :firebase_user_not_found
        })

      {:get_firebase_user, error} ->
        Logger.error("Error fetching Firebase user: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          message: "Failed to fetch Firebase user information",
          error_code: :firebase_user_fetch_failed
        })

      {:get_phone, nil} ->
        Logger.error("User has no phone number: #{external_id}")

        conn
        |> put_status(:bad_request)
        |> json(%{
          message: "User has no phone number associated with their account",
          error_code: :user_no_phone_number
        })

      {:allowed_phone, false} ->
        Logger.info("Phone number not allowed for deletion: #{inspect(Users.get_by(external_id: external_id))}")

        conn
        |> put_status(:forbidden)
        |> json(%{
          message: "Your phone number is not in the allowed list for account deletion",
          error_code: :phone_number_not_allowed
        })

      {:delete_firebase, error} ->
        Logger.error("Error deleting Firebase user: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          message: "Failed to delete Firebase user",
          error_code: :firebase_user_deletion_failed
        })

      {:mark_deleted, error} ->
        Logger.error("Error marking user as deleted: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          message: "Failed to mark user as deleted",
          error_code: :user_deletion_failed
        })
    end
  end

  def delete_user(%{assigns: %{current_user_id: external_id, user_payload: %{"roles" => roles}}} = conn, _params) do
    with {_, nil} <-
           {:user_exists, Users.get_by(external_id: external_id)},
         {_, nil} <- {:promoter_check, Enum.find(roles, &(&1 == "PROMOTER"))},
         {_, nil} <- {:admin_check, Enum.find(roles, &(&1 == "ADMIN"))},
         {_, {:ok, _response}} <-
           {:delete, FirebaseAdmin.delete_user(external_id)} do
      conn
      |> put_status(:no_content)
      |> text("")
    else
      {:user_exists, %User{}} ->
        conn
        |> put_status(:conflict)
        |> json(%{
          message: "Can't delete a firebase user that already has a user profile",
          error_code: :user_profile_already_exists
        })

      {:promoter_check, _} ->
        conn
        |> put_status(:forbidden)
        |> json(%{
          message: "Promoter users cannot be deleted",
          error_code: :user_deletion_forbidden
        })

      {:admin_check, _} ->
        conn
        |> put_status(:forbidden)
        |> json(%{
          message: "Admin users cannot be deleted",
          error_code: :user_deletion_forbidden
        })

      {:delete, error} ->
        Logger.error("Error deleting Firebase user: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          message: "Failed to delete firebase user",
          error_code: :firebase_user_deletion_failed
        })
    end
  end

  # delete user without a role, e.g. users during the registration process
  def delete_user(%{assigns: %{current_user_id: external_id}} = conn, _params) do
    with {_, nil} <-
           {:user_exists, Users.get_by(external_id: external_id)},
         {_, {:ok, _response}} <-
           {:delete, FirebaseAdmin.delete_user(external_id)} do
      conn
      |> put_status(:no_content)
      |> text("")
    else
      {:user_exists, %User{}} ->
        conn
        |> put_status(:conflict)
        |> json(%{
          message: "Can't delete a firebase user that already has a user profile",
          error_code: :user_profile_already_exists
        })

      {:delete, error} ->
        Logger.error("Error deleting Firebase user: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          message: "Failed to delete firebase user",
          error_code: :firebase_user_deletion_failed
        })
    end
  end

  operation :create_user,
    summary: "Create a user for a firebase user",
    parameters: [
      email: [
        in: :body,
        description: "User email",
        type: :string,
        example: "<EMAIL>"
      ],
      firstName: [
        in: :body,
        description: "User first name",
        type: :string,
        example: "Max"
      ],
      lastName: [
        in: :body,
        description: "User last name",
        type: :string,
        example: "Mustermann"
      ]
    ],
    responses: %{
      201 => {"Create User Response", "application/json", CreateUserResponse},
      400 => %Reference{"$ref": "#/components/responses/400"},
      401 => %Reference{"$ref": "#/components/responses/401"},
      409 => %Reference{"$ref": "#/components/responses/409"},
      500 => %Reference{"$ref": "#/components/responses/500"}
    }

  @spec create_user(Plug.Conn.t(), any()) :: Plug.Conn.t()
  def create_user(
        %{assigns: %{user_token_provider: :FIREBASE, current_user_id: external_user_id, token: token}} = conn,
        params
      )
      when not is_nil(external_user_id) do
    Logger.debug("Create user with params #{inspect(params)}")
    email = params["email"]
    given_name = params["givenName"] || params["firstName"]
    family_name = params["familyName"] || params["lastName"]
    display_name = "#{given_name} #{family_name}"

    with {_, nil} <-
           {:exists, Users.get_by(external_id: external_user_id)},
         {_, {:ok, %{account: %{user: user}}}} <-
           {:create, Firebase.create_for_existing_firebase_user(external_user_id, token, params)},
         {_, {:ok, _firebase_user}} <-
           {:add_custom_claim, Firebase.update_internal_user_id(external_user_id, user.id)},
         {_, {:ok, %{"oobLink" => verification_link}}} <-
           {:verify, FirebaseAdmin.generate_email_verification_link(email)},
         {_, {:ok, token, _claims}} <-
           {:token, FirebaseAdmin.generate_custom_token(external_user_id)} do
      case Mailer.send_verification_mail(email, display_name, verification_link) do
        :ok ->
          Logger.debug("Verification email sent to #{email}")

        {:error, error} ->
          Logger.error("Error sending verification email: #{inspect(error)}")
      end

      conn
      |> put_status(:created)
      |> json(%{idToken: token, user: %{id: user.id}})
    else
      {:exists, _user} ->
        conn
        |> put_status(:conflict)
        |> text("Der Benutzer existiert bereits.")

      {:create, {:error, :firebase_user, %{status: 400}, _account_data} = error} ->
        Logger.error("Error creating Firebase user: #{inspect(error)}")

        conn
        |> put_status(:bad_request)
        |> text("Der Benutzer konnte nicht erstellt werden.")

      {:create, {:error, :firebase_user, %{status: 422}, _account_data} = error} ->
        Logger.error("Error creating Firebase user: #{inspect(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> text("Der Benutzer konnte nicht erstellt werden.")

      {:create, {:error, %{status: 400}, _account_data} = error} ->
        Logger.error("Error creating Firebase user: #{inspect(error)}")

        case maybe_get_firebase_error_message(error) do
          "CREDENTIAL_TOO_OLD_LOGIN_AGAIN" ->
            conn
            |> put_status(:unauthorized)
            |> text("Der Benutzer konnte nicht erstellt werden.")

          _ ->
            conn
            |> put_status(:bad_request)
            |> text("Der Benutzer konnte nicht erstellt werden.")
        end

      {:create, {:error, %{status: 422}, _account_data} = error} ->
        Logger.error("Error creating Firebase user: #{inspect(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> text("Der Benutzer konnte nicht erstellt werden.")

      {:create, {:error, %{status: 400}} = error} ->
        Logger.error("Error creating Firebase user: #{inspect(error)}")

        case maybe_get_firebase_error_message(error) do
          "CREDENTIAL_TOO_OLD_LOGIN_AGAIN" ->
            conn
            |> put_status(:unauthorized)
            |> text("Der Benutzer konnte nicht erstellt werden.")

          _ ->
            conn
            |> put_status(:bad_request)
            |> text("Der Benutzer konnte nicht erstellt werden.")
        end

      {:create, {:error, %{status: 422}} = error} ->
        Logger.error("Error creating Firebase user: #{inspect(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> text("Der Benutzer konnte nicht erstellt werden.")

      {:create, {:error, :user, changeset_error, _}} ->
        Logger.error("Error creating user: #{inspect(changeset_error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> text("Der Benutzer konnte nicht erstellt werden.")

      {:create, error} ->
        Logger.error("Error creating user: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> text("Der Benutzer konnte nicht erstellt werden.")

      {:add_custom_claim, _} ->
        Logger.error("Error updating Firebase user with internal user ID")

        conn
        |> put_status(:internal_server_error)
        |> text("Der Benutzer konnte nicht aktualisiert werden.")

      {:update, error} ->
        Logger.error("Error updating Firebase user: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> text("Der Benutzer konnte nicht aktualisiert werden.")

      {:verify, error} ->
        Logger.error("Error verifying email: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> text("Email zur Verifizierung konnte nicht erstellt werden.")

      {:token, {:error, error}} ->
        Logger.error("Error generating custom token: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> text("Token konnte nicht erstellt werden.")
    end
  end

  def create_user(conn, _params) do
    conn
    |> put_status(:forbidden)
    |> text("User is not a Firebase user")
  end

  operation :signin,
    summary: "Sign in a user",
    parameters: [
      email: [
        in: :body,
        description: "User email",
        type: :string,
        required: false,
        example: "<EMAIL>"
      ],
      phoneNumber: [
        in: :body,
        description: "User phone number",
        type: :string,
        required: false,
        example: "+49123456789"
      ]
    ],
    responses: %{
      200 => {"Sign In Response", "application/json", SignInResponse},
      400 => %Reference{"$ref": "#/components/responses/400"},
      500 => %Reference{"$ref": "#/components/responses/500"}
    }

  @spec signin(Plug.Conn.t(), any()) :: Plug.Conn.t()
  def signin(conn, params) do
    with {:user, {:ok, %{"users" => [user | _]}}} <-
           {:user, Firebase.get_user(params)},
         {:has_email, email} when not is_nil(email) <-
           {:has_email, user["email"]},
         {:link, {:ok, %{"oobLink" => verification_link}}} <-
           {:link, Firebase.generate_link(email, user["emailVerified"])},
         {:mail, :ok} <-
           {:mail,
            Firebase.send_link(
              email,
              user["displayName"],
              verification_link,
              user["emailVerified"]
            )} do
      status = if user["emailVerified"], do: "sign_in_link_sent", else: "email_not_verified"

      conn
      |> put_status(:ok)
      |> json(%{status: status})
    else
      {:user, _res} ->
        conn
        |> put_status(:ok)
        |> json(%{status: "registration_incomplete"})

      {:has_email, nil} ->
        conn
        |> put_status(:ok)
        |> json(%{status: "registration_incomplete"})

      {:link, _res} ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{error: "Could not generate link."})

      {:mail, {:error, msg}} ->
        Logger.error("Error sending verification email: #{inspect(msg)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{error: "Could not send verification email."})
    end
  end

  operation :reset_password,
    summary: "Reset user password",
    parameters: [
      email: [
        in: :body,
        description: "User email",
        type: :string,
        required: false,
        example: "<EMAIL>"
      ]
    ],
    responses: %{
      200 => {"Reset Password Response", "application/json", ResetPasswordResponse},
      400 => %Reference{"$ref": "#/components/responses/400"},
      500 => %Reference{"$ref": "#/components/responses/500"}
    }

  @spec reset_password(Plug.Conn.t(), map()) :: Plug.Conn.t()
  def reset_password(conn, %{"email" => email}) do
    with {:user, {:ok, %{"users" => [user | _]}}} <-
           {:user, FirebaseAdmin.get_user_by_email(email)},
         {:link, {:ok, %{"oobLink" => reset_link}}} <-
           {:link, FirebaseAdmin.generate_password_reset_link(email)},
         {:mail, :ok} <-
           {:mail, Mailer.send_password_reset_link(email, user["displayName"], reset_link)} do
      conn
      |> put_status(:ok)
      |> json(%{status: "password_reset_link_sent"})
    else
      {:user, error} ->
        Logger.error("Error getting user: #{inspect(error)}")

        conn
        |> put_status(:bad_request)
        |> text("Benutzer konnte nicht gefunden werden.")

      {:link, %{status: 400}} = error ->
        Logger.error("Error generating password reset link: #{inspect(error)}")

        conn
        |> put_status(:bad_request)
        |> text("Passwort zurücksetzen konnte nicht angefordert werden.")

      {:link, %{status: 422}} = error ->
        Logger.error("Error generating password reset link: #{inspect(error)}")

        conn
        |> put_status(:unprocessable_entity)
        |> text("Passwort zurücksetzen konnte nicht angefordert werden.")

      {:link, error} ->
        Logger.error("Error generating password reset link: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> text("Passwort zurücksetzen konnte nicht angefordert werden.")

      {:mail, {:error, msg}} ->
        Logger.error("Error sending password reset email: #{inspect(msg)}")

        conn
        |> put_status(:internal_server_error)
        |> text("Passwort zurücksetzen konnte nicht angefordert werden.")
    end
  end

  operation :signin_security,
    summary: "Sign in a security user",
    parameters: [
      token: [
        in: :body,
        description: "User token",
        type: :string,
        required: true,
        example: "eyJhbGciOi"
      ]
    ],
    responses: %{
      200 => {"Sign In Security User  Response", "application/json", SigninSecurityResponse},
      400 => %Reference{"$ref": "#/components/responses/400"},
      401 => %Reference{"$ref": "#/components/responses/401"},
      500 => %Reference{"$ref": "#/components/responses/500"}
    }

  def signin_security(conn, %{"token" => token}) do
    with {_, {:ok, decoded_token}} <-
           {:token_decode, Token.verify_and_decode(token)},
         {_, %User{provider: provider} = _user} <-
           {:user, get_user_by_uid(decoded_token["uid"])},
         {_, {:ok, access_token}} <-
           {:token_create, get_token_by_provider(provider, decoded_token["uid"])} do
      conn
      |> put_status(:ok)
      |> json(%{idToken: access_token})
    else
      {:token_decode, {:error, error}} ->
        Logger.error("Could not decode token: #{inspect(error)}")

        conn
        |> put_status(:unauthorized)
        |> json(%{
          error_code: :invalid_token,
          message: "Invalid token provided."
        })

      {:user, nil} ->
        Logger.error("User not found for token #{token}")

        conn
        |> put_status(:not_found)
        |> json(%{
          error_code: :user_not_found,
          message: "User in token was not found."
        })

      {:token_create, {:error, error}} ->
        Logger.error("An error occurred trying to create a custom token: #{inspect(error)}")

        conn
        |> put_status(:internal_server_error)
        |> json(%{
          error_code: :service_error,
          message: "Unable to generate access token for user."
        })
    end
  end

  operation :userinfo,
    summary: "Userinfo for current user",
    responses: %{
      :ok => {"User response", "application/json", UserResponse},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :unauthorized => %Reference{"$ref": "#/components/responses/unauthorized"}
    }

  ## Detailed userinfo is only available to logged-in users for themselves.
  def userinfo(%{assigns: %{current_user_id: user_id}} = conn, _params) do
    Logger.debug("/accounts/api/userinfo called for current user: #{user_id}")

    with {_, %User{id: user_id} = user} <- {:user, get_user_by_uid(user_id)},
         {_, %UserProfile{id: user_profile_id} = user_profile} <-
           {:user_profile, Users.get_user_profile_by_user_id(user_id)} do
      user_settings =
        Users.get_user_settings_by_user_profile_id(user_profile_id) ||
          %{receive_first_party_emails: false, receive_third_party_emails: false}

      conn
      |> put_status(:ok)
      |> put_resp_header("Cache-Control", "private")
      |> render(:userinfo,
        userinfo: %{
          user: user,
          user_profile: user_profile,
          user_settings: user_settings
        }
      )
    else
      {:user, _} ->
        Logger.error("User with #{inspect(user_id)} not found")

        conn
        |> put_status(:not_found)
        |> json(%{error_code: :user_not_found, message: "User not found"})

      {:user_profile, _} ->
        Logger.error("UserProflile for User #{inspect(user_id)} not found")

        conn
        |> put_status(:not_found)
        |> json(%{error_code: :user_not_found, message: "User not found"})
    end
  end

  defp get_token_by_provider(:CASDOOR, uid), do: CasdoorUsers.get_security_access_token(uid)

  defp get_token_by_provider(_provider, uid) do
    case FirebaseAdmin.generate_custom_token(uid) do
      {:ok, token, _claims} ->
        {:ok, token}

      error ->
        error
    end
  end

  defp get_user_by_uid(uid) do
    if uuid?(uid) do
      Users.get_user(uid)
    else
      Users.get_by(external_id: uid)
    end
  end

  defp uuid?(string) do
    case Ecto.UUID.cast(string) do
      {:ok, _} -> true
      :error -> false
    end
  end

  defp maybe_authorize(%{params: %{"provider" => "CASDOOR"}} = conn, opts) do
    opts = Authorize.init(opts)
    Authorize.call(conn, opts)
  end

  defp maybe_authorize(conn, _opts), do: conn

  defp maybe_ensure_authenticated(%{params: %{"provider" => "CASDOOR"}} = conn, opts) do
    opts = EnsureAuthenticated.init(opts)
    EnsureAuthenticated.call(conn, opts)
  end

  defp maybe_ensure_authenticated(conn, _opts), do: conn

  defp get_allowed_phone_numbers do
    case Application.get_env(:accounts_service, :environment) do
      :prod -> ["+************"]
      _ -> ["+************"]
    end
  end

  defp normalise_phone(phone), do: String.replace(phone, ~r/\s+/, "")

  defp maybe_get_firebase_error_message({:error, %{body: %{"error" => %{"message" => message}}}}) do
    message
  end

  defp maybe_get_firebase_error_message(_message), do: nil
end
