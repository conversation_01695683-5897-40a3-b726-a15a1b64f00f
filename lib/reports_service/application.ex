defmodule ReportsService.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    Logger.configure(level: String.to_atom(System.get_env("LOG_LEVEL", "info")))

    children = [
      ReportsServiceWeb.Telemetry,
      ReportsService.Repo,
      {Phoenix.PubSub, name: ReportsService.PubSub},
      # Start a worker by calling: ReportsService.Worker.start_link(arg)
      # {ReportsService.Worker, arg},
      # Start to serve requests, typically the last entry
      ReportsServiceWeb.Endpoint,
      ExRBAC.Supervisor,
      {Money.ExchangeRates.Supervisor, restart: true, start_retriever: true},
      {Oban, Application.fetch_env!(:reports_service, Oban)},
      # Start DB Heartbeat worker
      {ReportsService.Workers.DbHeartbeatWorker, 0}
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: ReportsService.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell <PERSON> to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    ReportsServiceWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
