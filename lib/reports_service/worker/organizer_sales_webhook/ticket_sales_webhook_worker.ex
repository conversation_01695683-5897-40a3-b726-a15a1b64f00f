defmodule ReportsService.Workers.OrganizerSalesWebhook.TicketSalesWebhookWorker do
  @moduledoc """
  Oban worker that executes the ticket sales report per organizer and sends them via a webhook.
  """

  use Oban.Worker, queue: :organizer_sales_webhook_report_generation, max_attempts: 3

  alias Ecto.Multi
  alias ReportsService.Events.Event
  alias ReportsService.Events.Voucher
  alias ReportsService.Orders.Ticket
  alias ReportsService.Orders.TicketExporter
  alias ReportsService.Repo
  alias ReportsService.Seller.OrganizerSalesWebhook
  alias ReportsService.Seller.OrganizerSalesWebhookHistory
  alias ReportsService.Util.DateFormatter

  require Logger

  @events_preloads [
    variants: [:mlpm, :ticket_category, sales_channel: [:channel_config]]
  ]

  @ticket_preloads [
    :ticket_history,
    :attendee,
    :owner,
    :swap_transaction,
    order_ticket: [
      :bill,
      order: [:billing_address, :created_by_personal_information, :payin_transaction]
    ]
  ]

  @impl Oban.Worker
  def perform(
        %{
          args: %{
            "end_date" => end_date,
            "execution_id" => execution_id,
            "organizer_id" => organizer_id,
            "start_date" => start_date,
            "url" => url
          }
        } = _job
      ) do
    Logger.info(
      "Performing organizer sales report for organizer #{organizer_id} between #{start_date} and #{end_date} with execution_id #{execution_id}"
    )

    latest_history = OrganizerSalesWebhookHistory.get_latest_by_execution_id(execution_id)

    with {_, {:ok, init_status}} <-
           {:update_init_status, maybe_update_status(latest_history, :PROCESSING, nil)},
         {_, tickets} <-
           {:get_tickets, Ticket.get_reportable_by_organizer_id(organizer_id, start_date, end_date, @ticket_preloads)},
         unique_event_ids = get_unique_event_ids(tickets),
         unique_voucher_ids = get_unique_voucher_ids(tickets),
         {_, events} <-
           {:get_events, Event.get_by_ids(unique_event_ids, @events_preloads)},
         {_, vouchers} <- {:get_vouchers, Voucher.get_by_ids(unique_voucher_ids)},
         {_, ticket_report} <-
           {:exportable_tickets, TicketExporter.build_exportable_tickets_info(tickets, events, vouchers)},
         {_, {:ok, result}} <-
           {:execute_webhook, execute_webhook(url, ticket_report, end_date, start_date, execution_id)} do
      maybe_update_status(init_status, :FINISHED, result)
      Logger.info("Finished organizer sales report with execution_id #{execution_id}")
      :ok
    else
      {:update_init_status, error} ->
        Logger.critical(
          "Failed to update webhook history init status for execution #{inspect(execution_id)} due to error #{inspect(error)}"
        )

        maybe_update_status(latest_history, :FAILED, nil)
        {:error, "Failed to update status"}

      {:execute_webhook, error} ->
        Logger.critical("Failed to execute webhook for execution #{inspect(execution_id)} due to error #{inspect(error)}")
        maybe_update_status(latest_history, :FAILED, error)
        {:error, "Failed to execute webhook"}
    end
  end

  def perform(job) do
    Logger.critical("Couldn't generate promoter sales report due to missing job arguments for job #{inspect(job)}")
    {:cancel, "Couldn't generate promoter sales report due to missing job arguments"}
  end

  @spec queue(webhooks :: [OrganizerSalesWebhook.t()], start_date :: DateTime.t(), end_date :: DateTime.t()) ::
          {:ok, Oban.Job.t()} | {:error, Oban.Job.changeset() | String.t()}
  def queue(_webhooks, nil, _end_date), do: {:error, "start_date missing"}
  def queue(_webhooks, _start_date, nil), do: {:error, "end_date missing"}

  def queue(webhooks, start_date, end_date) when is_list(webhooks) do
    multi =
      Enum.reduce(webhooks, Multi.new(), fn webhook, acc ->
        Multi.run(acc, {:oban_job, webhook.id}, fn _repo, _changes ->
          webhook
          |> build_oban_job_props(start_date, end_date)
          |> new()
          |> Oban.insert()
        end)
      end)

    Repo.transaction(multi)
  end

  def queue(_webhook, _start_date, _end_date) do
    {:error, "Couldn't queue report generation jobs due to parameter"}
  end

  defp build_oban_job_props(webhook, start_date, end_date) do
    %{
      "organizer_id" => webhook.organizer_id,
      "execution_id" => webhook.execution_id,
      "start_date" => start_date,
      "end_date" => end_date,
      "url" => webhook.url
    }
  end

  defp get_unique_event_ids(tickets) do
    tickets
    |> Enum.map(& &1.event_id)
    |> Enum.uniq()
  end

  defp get_unique_voucher_ids(tickets) do
    tickets
    |> Enum.map(& &1.voucher_id)
    |> Enum.reject(&is_nil/1)
    |> Enum.uniq()
  end

  defp execute_webhook(webhook_url, ticket_report, end_date, start_date, execution_id) do
    payload = create_payload(ticket_report, end_date, start_date, execution_id)

    client()
    |> Tesla.post(webhook_url, payload)
    |> handle_response()
  end

  defp client do
    middleware = [
      Tesla.Middleware.JSON,
      {Tesla.Middleware.Logger, debug: true},
      {Tesla.Middleware.Timeout, timeout: 15_000},
      {Tesla.Middleware.Retry,
       delay: 500,
       max_retries: 3,
       max_delay: 3_000,
       should_retry: fn
         {:ok, %Tesla.Env{status: status}} when status in 500..599 -> true
         {:error, _} -> true
         _ -> false
       end}
    ]

    Tesla.client(middleware, Tesla.Adapter.Hackney)
  end

  defp handle_response({:ok, response}) do
    {:ok, response.body}
  end

  defp handle_response({:error, error}) do
    Logger.error("Webhook HTTP request failed due to error: #{inspect(error)}")

    {:error, error}
  end

  defp create_payload(ticket_report, end_date, start_date, execution_id) do
    %{
      data: ticket_report,
      end_date: DateFormatter.string_to_iso8601(end_date),
      execution_id: execution_id,
      start_date: DateFormatter.string_to_iso8601(start_date),
      created_at: DateTime.truncate(DateTime.utc_now(), :millisecond)
    }
  end

  defp maybe_update_status(nil, status, _response) do
    Logger.error("Failed to update webhook history status to #{inspect(status)} due to missing previous history")
  end

  defp maybe_update_status(history, status, response),
    do: OrganizerSalesWebhookHistory.update_status(history, status, response)
end
