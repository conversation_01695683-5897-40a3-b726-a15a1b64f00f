defmodule ReportsService.Workers.OrganizerSalesWebhook.InitWorker do
  @moduledoc """
  Oban worker that initiates ticket sales report per organizer and sends them via a webhook.

  For each entry in OrganizerSalesWebhook a new Oban job will be created, which calculates the
  ticket sales for all events of an organizer within a specified time frame
  """

  use Oban.Worker, queue: :organizer_sales_webhook_init, max_attempts: 5

  alias ReportsService.Seller.OrganizerSalesWebhook
  alias ReportsService.Seller.OrganizerSalesWebhookHistory
  alias ReportsService.Workers.OrganizerSalesWebhook.TicketSalesWebhookWorker

  require Logger

  @impl Oban.Worker
  def perform(_job) do
    Logger.info("Performing organizer sales webhook initialization ...")

    yesterday = Date.add(Date.utc_today(), -1)
    start_date = NaiveDateTime.new!(yesterday, ~T[00:00:00])
    end_date = NaiveDateTime.new!(Date.add(yesterday, 1), ~T[00:00:00])

    with {_, [_ | _] = webhooks} <- {:get_webhooks, OrganizerSalesWebhook.get_active_webhooks()},
         {_, {:ok, init_histories}} <-
           {:insert_histories, OrganizerSalesWebhookHistory.create_init_entries(webhooks, start_date, end_date)},
         {_, populated_webhooks} <- {:populate_webhooks, backfill_execution_id(webhooks, init_histories)},
         {_, {:ok, _insert_result}} <-
           {:insert_generator_jobs, TicketSalesWebhookWorker.queue(populated_webhooks, start_date, end_date)} do
      Logger.info("Finished organizer sales webhook initialization for #{length(webhooks)} webhooks!")
      :ok
    else
      {:get_webhooks, []} ->
        Logger.info("No active webhook configurations found. Finished initialization!")
        :ok

      {:get_webhooks, error} ->
        Logger.error("Failed to get active webhook configurations due to error #{inspect(error)}")
        {:error, "Failed to get active webhook configurations"}

      {:insert_histories, error} ->
        Logger.error("Failed to create webhook init history entries due to error #{inspect(error)}")
        {:error, "Failed to create webhook init history entries"}

      {:insert_generator_jobs, error} ->
        Logger.error("Failed to enqueue report generation jobs due to error #{inspect(error)}")
        {:error, "Failed to enqueue report generation jobs"}

      {_, error} ->
        Logger.error("Failed to initialize ticket sales webhooks due to error #{inspect(error)}")
        {:error, "Failed to initialize ticket sales webhooks"}
    end
  end

  defp backfill_execution_id(webhooks, init_histories) do
    normalized_histories = init_histories |> Map.values() |> Enum.filter(&is_struct/1)

    Enum.map(webhooks, fn webhook ->
      normalized_histories
      |> Enum.find(fn history -> history.organizer_sales_webhook_id == webhook.id end)
      |> case do
        nil -> webhook
        history -> %{webhook | execution_id: history.execution_id}
      end
    end)
  end
end
