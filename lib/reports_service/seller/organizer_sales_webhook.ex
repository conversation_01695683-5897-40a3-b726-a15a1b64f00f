defmodule ReportsService.Seller.OrganizerSalesWebhook do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query

  alias ReportsService.Repo
  alias ReportsService.Seller.OrganizerSalesWebhook
  alias ReportsService.Seller.OrganizerSalesWebhookHistory

  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          organizer_id: Ecto.UUID.t(),
          url: String.t(),
          deleted_at: DateTime.t(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "organizer_sales_webhook" do
    field :organizer_id, Ecto.UUID
    field(:execution_id, :string, virtual: true)
    field :url, :string
    field :deleted_at, :utc_datetime

    has_many :organizer_sales_webhook_histories, OrganizerSalesWebhookHistory

    timestamps(type: :utc_datetime)
  end

  def changeset(webhook, attrs) do
    webhook
    |> cast(attrs, [:organizer_id, :url, :deleted_at])
    |> validate_required([:organizer_id, :url])
  end

  @spec get_active_webhooks() :: [OrganizerSalesWebhook.t()]
  def get_active_webhooks do
    query =
      from(osw in OrganizerSalesWebhook,
        where: is_nil(osw.deleted_at)
      )

    Repo.all(query)
  end
end
