defmodule ReportsService.Seller.OrganizerSalesWebhookHistory do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query

  alias Ecto.Multi
  alias ReportsService.Repo
  alias ReportsService.Seller.OrganizerSalesWebhook
  alias ReportsService.Seller.OrganizerSalesWebhookHistory

  @type status :: :CREATED | :PROCESSING | :SENDING | :FINISHED | :FAILED
  @status_types [
    :CREATED,
    :PROCESSING,
    :SENDING,
    :FAILED,
    :FINISHED
  ]

  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          organizer_sales_webhook_id: Ecto.UUID.t(),
          report_start_date: DateTime.t(),
          report_end_date: DateTime.t(),
          status_before: status,
          status_after: status,
          response: map(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "organizer_sales_webhook_history" do
    belongs_to :organizer_sales_webhook, OrganizerSalesWebhook

    field :execution_id, Ecto.UUID
    field :report_start_date, :utc_datetime
    field :report_end_date, :utc_datetime
    field :status_before, Ecto.Enum, values: @status_types
    field :status_after, Ecto.Enum, values: @status_types
    field :response, :map

    timestamps(type: :utc_datetime)
  end

  def changeset(history, attrs) do
    history
    |> cast(attrs, [
      :organizer_sales_webhook_id,
      :execution_id,
      :report_start_date,
      :report_end_date,
      :status_before,
      :status_after,
      :response
    ])
    |> validate_required([
      :organizer_sales_webhook_id,
      :execution_id,
      :report_start_date,
      :report_end_date,
      :status_after
    ])
  end

  @spec create(attrs :: map()) :: {:ok, OrganizerSalesWebhookHistory.t()} | {:error, Ecto.Changeset.t()}
  def create(attrs \\ %{}) do
    %OrganizerSalesWebhookHistory{}
    |> OrganizerSalesWebhookHistory.changeset(attrs)
    |> Repo.insert()
  end

  @spec create_init_entries(webhooks :: [OrganizerSalesWebhook.t()], start_date :: any(), end_date :: any()) ::
          {:ok, any()}
          | {:error, atom(), any(), any()}

  def create_init_entries(webhooks, start_date, end_date) do
    multi =
      Enum.reduce(webhooks, Multi.new(), fn webhook, acc ->
        changeset =
          OrganizerSalesWebhookHistory.changeset(
            %OrganizerSalesWebhookHistory{},
            %{
              organizer_sales_webhook_id: webhook.id,
              execution_id: Ecto.UUID.generate(),
              report_start_date: start_date,
              report_end_date: end_date,
              status_before: nil,
              status_after: :CREATED,
              response: %{}
            }
          )

        Multi.insert(acc, {:history, webhook.id}, changeset)
      end)

    Repo.transaction(multi)
  end

  @spec update_status(history :: OrganizerSalesWebhookHistory.t(), new_status :: status) ::
          {:ok, OrganizerSalesWebhookHistory.t()} | {:error, Ecto.Changeset.t()}
  @spec update_status(history :: OrganizerSalesWebhookHistory.t(), new_status :: status, response :: map()) ::
          {:ok, OrganizerSalesWebhookHistory.t()} | {:error, Ecto.Changeset.t()}
  def update_status(%OrganizerSalesWebhookHistory{} = history, new_status, response \\ %{}) do
    attrs = %{
      organizer_sales_webhook_id: history.organizer_sales_webhook_id,
      execution_id: history.execution_id,
      report_start_date: history.report_start_date,
      report_end_date: history.report_end_date,
      status_before: history.status_after,
      status_after: new_status,
      response: response
    }

    %OrganizerSalesWebhookHistory{}
    |> OrganizerSalesWebhookHistory.changeset(attrs)
    |> Repo.insert()
  end

  @spec get_by_webhook_id(webhook_id :: Ecto.UUID.t()) :: [OrganizerSalesWebhookHistory.t()]
  def get_by_webhook_id(webhook_id) do
    Repo.all(from h in OrganizerSalesWebhookHistory, where: h.organizer_sales_webhook_id == ^webhook_id)
  end

  @spec get_latest_by_execution_id(execution_id :: Ecto.UUID.t()) :: OrganizerSalesWebhookHistory.t() | nil
  def get_latest_by_execution_id(execution_id) do
    OrganizerSalesWebhookHistory
    |> where([h], h.execution_id == ^execution_id)
    |> order_by([h], desc: h.inserted_at)
    |> limit(1)
    |> Repo.one()
  end
end
