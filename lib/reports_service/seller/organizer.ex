defmodule ReportsService.Seller.Organizer do
  @moduledoc false
  use Ecto.Schema

  alias ReportsService.Events.Organizer, as: EventsOrganizer

  # styler:sort
  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          promoter_id: Ecto.UUID.t(),
          deleted_at: DateTime.t() | nil,
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  schema "organizers" do
    belongs_to :organizer, EventsOrganizer, foreign_key: :promoter_id

    field :deleted_at, :utc_datetime

    timestamps(type: :utc_datetime)
  end
end
