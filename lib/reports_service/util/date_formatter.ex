defmodule ReportsService.Util.DateFormatter do
  @moduledoc """
  Util to format the cent amounts from the database to a human readable money string.
    Will convert 120000 to 1.200,00 €
  """

  @spec format(Timex.Time.t()) :: String.t()
  @doc """
  Returns date string with the format "{0D}.{0M}.{YYYY} {h24}:{m} UTC +/-{offset}" e.g. 29.08.2024 15:34:21 UTC +2
  """
  def format(datetime) do
    formatted_date = Timex.format!(datetime, "{0D}.{0M}.{YYYY} {h24}:{m}")
    {offset_sign, offset_hours} = get_timezone_offset(datetime)
    "#{formatted_date} UTC #{offset_sign}#{offset_hours}"
  end

  def string_to_iso8601(date_string) do
    {:ok, naive} = NaiveDateTime.from_iso8601(date_string)
    datetime = DateTime.from_naive!(naive, "Etc/UTC")
    DateTime.to_iso8601(datetime)
  end

  defp get_timezone_offset(%DateTime{utc_offset: offset, std_offset: std_offset}) do
    total_offset = offset + std_offset
    hours_offset = div(total_offset, 3600)

    offset_sign = if hours_offset >= 0, do: "+", else: "-"
    {offset_sign, abs(hours_offset)}
  end
end
