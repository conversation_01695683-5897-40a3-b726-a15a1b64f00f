defmodule ReportsService.Orders.PersonalInformation do
  @moduledoc false

  use Ecto.Schema

  # styler:sort
  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          email: String.t(),
          family_name: String.t(),
          gender: :"0" | :"1" | :"2" | :"9",
          given_name: String.t(),
          inserted_at: DateTime.t(),
          phone_number: String.t(),
          birthdate: Date.t(),
          deleted_at: DateTime.t() | nil,
          user_id: Ecto.UUID.t(),
          user_document_id: String.t(),
          updated_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  @schema_prefix "orders"

  # styler:sort
  schema "personal_informations" do
    field :birthdate, :date
    field :city, :string
    field :country_iso, :string

    field :deleted_at, :utc_datetime

    field :email, :string
    field :family_name, :string

    field :gender, Ecto.Enum, values: [:"0", :"1", :"2", :"9"]

    # names can be "bo_sale" which means that an order came from a booking office. discussed with @boes and @kap
    field :given_name, :string
    field :phone_number, :string
    field :postal_code, :string
    field :user_document_id, :string
    field :user_id, Ecto.UUID
    timestamps()
  end
end
