defmodule ReportsService.Orders.Ticket do
  @moduledoc false

  use Ecto.Schema

  import Ecto.Query

  alias ReportsService.Events.Event
  alias ReportsService.Events.EventPermission
  alias ReportsService.Events.MultiLevelPricingModifier
  alias ReportsService.Events.MultiLevelPricingModifierVariant
  alias ReportsService.Events.Organizer
  alias ReportsService.Events.TicketCategory
  alias ReportsService.Events.Variant
  alias ReportsService.Events.Voucher
  alias ReportsService.Orders.Bill
  alias ReportsService.Orders.Order
  alias ReportsService.Orders.OrderTicket
  alias ReportsService.Orders.PersonalInformation
  alias ReportsService.Orders.SwapTransaction
  alias ReportsService.Orders.Ticket
  alias ReportsService.Orders.TicketGroup
  alias ReportsService.Orders.TicketHistory
  alias ReportsService.Repo
  alias ReportsService.SellerPermissions

  @distribution_types [:GUEST_LIST_INVITATION, :SALES_CHANNEL, :REGULAR]

  @ticket_status [
    :ACTIVE,
    :CREATED,
    :DEFRAUDED,
    :INVITATION_REJECTED,
    :PENDING,
    :REFUNDED,
    :TIMEDOUT,
    :USED,
    :UNUSED,
    :SWAPPED
  ]

  @ticket_type [:DIGITAL, :HARD]

  @reportable_ticket_status [
    :ACTIVE,
    :USED,
    :UNUSED,
    :SWAPPED
  ]

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  @schema_prefix "orders"
  # styler:sort
  schema "tickets" do
    belongs_to(:attendee, PersonalInformation, foreign_key: :attendee_id)
    belongs_to(:owner, PersonalInformation, foreign_key: :owner_id)
    field(:admission, :boolean)
    field(:category_id, :binary_id)
    field(:category_name, :string, virtual: true)
    field(:channel_config_id, :binary_id)
    field(:distribution_type, Ecto.Enum, values: @distribution_types, default: :REGULAR)
    field(:event_id, :binary_id)
    field(:is_checkin_allowed, :boolean, default: false)
    field(:purchase_date, :utc_datetime)
    field(:status, Ecto.Enum, values: @ticket_status)
    field(:type, Ecto.Enum, values: @ticket_type)
    field(:variant, :map, virtual: true)
    field(:variant_id, :binary_id)
    field(:voucher_id, :binary_id)

    has_many(:ticket_history, TicketHistory, foreign_key: :ticket_id)
    has_one(:order_ticket, OrderTicket)

    has_one(:swap_transaction, SwapTransaction, references: :id, foreign_key: :new_ticket_id)
    timestamps(updated_at: false)
  end

  @spec get_ticket_sales_for_user(user_id :: Ecto.UUID.t() | String.t() | :ADMIN) :: [map()]
  @spec get_ticket_sales_for_user(user_id :: Ecto.UUID.t() | String.t() | :ADMIN, opts :: keyword()) :: [map()]
  def get_ticket_sales_for_user(user_id, opts \\ []) do
    admission = Keyword.get(opts, :admission)
    seller_id = Keyword.get(opts, :seller_id)

    query =
      user_id
      |> reportable_tickets_for_user_query(admission)
      |> maybe_filter_by_seller_events(seller_id)
      |> where(
        [
          _ticket,
          _order_ticket,
          _order,
          event,
          _event_permission,
          _bill,
          _organizer
        ],
        event.is_draft == false
      )
      |> group_by(
        [
          ticket,
          _order_ticket,
          _order,
          event,
          _event_permission,
          _bill,
          _organizer
        ],
        [
          fragment("?::date", ticket.purchase_date),
          event.id,
          event.title
        ]
      )
      |> order_by(
        [
          ticket,
          _order_ticket,
          _order,
          _event,
          _event_permission,
          _bill,
          _organizer
        ],
        desc: fragment("?::date", ticket.purchase_date)
      )
      |> select(
        [
          ticket,
          _order_ticket,
          _order,
          event,
          _event_permission,
          bill,
          _organizer
        ],
        %{
          date: fragment("?::date", ticket.purchase_date),
          event_id: event.id,
          event_title: event.title,
          sold_tickets:
            fragment(
              "COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)",
              ticket.id,
              ticket.admission,
              true
            ),
          sold_extras:
            fragment(
              "COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)",
              ticket.id,
              ticket.admission,
              false
            ),
          gross_sales: sum(bill.organizer_total)
        }
      )

    Repo.all(query)
  end

  def get_ticket_sales_for_user_and_event(user_id, event_id, distribution_types, admission \\ nil) do
    query =
      user_id
      |> reportable_tickets_for_user_query(admission)
      |> join(
        :inner,
        [
          ticket: t,
          order_ticket: ot,
          order: o,
          event: e,
          event_permission: ep,
          bill: b,
          organizer: org
        ],
        variant in Variant,
        as: :variant,
        on: t.variant_id == variant.id and is_nil(variant.deleted_at)
      )
      |> join(
        :inner,
        [
          ticket: t,
          order_ticket: ot,
          order: o,
          event: e,
          event_permission: ep,
          bill: b,
          organizer: org,
          variant: v
        ],
        category in TicketCategory,
        as: :category,
        on: t.category_id == category.id and is_nil(category.deleted_at)
      )
      |> join(
        :left,
        [
          ticket: t,
          order_ticket: ot,
          order: o,
          event: e,
          event_permission: ep,
          bill: b,
          organizer: org,
          variant: v,
          category: c
        ],
        voucher in Voucher,
        as: :voucher,
        on: t.voucher_id == voucher.id
      )
      |> join(
        :left,
        [
          ticket: t,
          order_ticket: ot,
          order: o,
          event: e,
          event_permission: ep,
          bill: b,
          organizer: org,
          variant: v,
          category: c,
          voucher: vo
        ],
        multi_level_pricing_modifier_variant in MultiLevelPricingModifierVariant,
        as: :mlpm_variant,
        on: v.id == multi_level_pricing_modifier_variant.variant_id
      )
      |> join(
        :left,
        [
          ticket: t,
          order_ticket: ot,
          order: o,
          event: e,
          event_permission: ep,
          bill: b,
          organizer: org,
          variant: v,
          category: c,
          voucher: vo,
          mlpm_variant: mlpmv
        ],
        multi_level_pricing_modifier in MultiLevelPricingModifier,
        as: :mlpm,
        on: mlpmv.mlpm_id == multi_level_pricing_modifier.id
      )
      |> join(
        :left,
        [
          ticket: t,
          order_ticket: ot,
          order: o,
          event: e,
          event_permission: ep,
          bill: b,
          organizer: org,
          variant: v,
          category: c,
          voucher: vo,
          mlpm_variant: mlpmv,
          mlpm: mlpm
        ],
        ticket_group in TicketGroup,
        as: :tg,
        on: t.ticket_group_id == ticket_group.id
      )
      |> where([event: e], e.id == ^event_id)
      |> maybe_filter_by_distribution_types(distribution_types)
      |> group_by(
        [
          ticket: t,
          order_ticket: ot,
          order: o,
          event: e,
          event_permission: ep,
          bill: b,
          organizer: org,
          variant: v,
          category: c,
          voucher: vo,
          mlpm_variant: mlpmv,
          mlpm: mlpm,
          tg: tg
        ],
        [
          v.id,
          v.unit_price,
          c.id,
          c.name,
          vo.id,
          vo.description,
          vo.value,
          b.discount,
          b.organizer_total,
          c.admission,
          mlpm.id,
          mlpm.label,
          tg.amount
        ]
      )
      |> order_by(
        [
          ticket: t,
          order_ticket: ot,
          order: o,
          event: e,
          event_permission: ep,
          bill: b,
          organizer: org,
          variant: v,
          category: c,
          voucher: vo,
          mlpm_variant: mlpmv,
          mlpm: mlpm,
          tg: tg
        ],
        desc: vo.value
      )
      |> select(
        [
          ticket: t,
          order_ticket: ot,
          order: o,
          event: e,
          event_permission: ep,
          bill: b,
          organizer: org,
          variant: v,
          category: c,
          voucher: vo,
          mlpm_variant: mlpmv,
          mlpm: mlpm,
          tg: tg
        ],
        %{
          ticket_category_id: c.id,
          ticket_category_name: c.name,
          unit_price: v.unit_price,
          voucher_id: vo.id,
          voucher_description: vo.description,
          voucher_value: vo.value,
          voucher_code: vo.code,
          discount: b.discount,
          sold_tickets: fragment("COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)", t.id, t.admission, true),
          sold_extras: fragment("COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)", t.id, t.admission, false),
          organizer_total: b.organizer_total,
          gross_sales: sum(b.organizer_total),
          admission: c.admission,
          mlpm_id: mlpm.id,
          mlpm_label: mlpm.label,
          ticket_group_amount: tg.amount
        }
      )

    Repo.all(query)
  end

  @spec get_sold_tickets_for_event_group_by_variant(event_id :: Ecto.UUID.t()) :: [map()]
  @spec get_sold_tickets_for_event_group_by_variant(
          event_id :: Ecto.UUID.t(),
          fitted_params :: %{
            admission: boolean() | nil,
            distribution_type: [atom()] | nil
          }
        ) :: [map()]
  def get_sold_tickets_for_event_group_by_variant(event_id) do
    event_id
    |> get_sold_tickets_for_event_group_by_variant_query()
    |> Repo.all()
  end

  def get_sold_tickets_for_event_group_by_variant(event_id, %{
        admission: admission,
        distribution_types: distribution_types
      }) do
    event_id
    |> get_sold_tickets_for_event_group_by_variant_query()
    |> maybe_filter_by_admission(admission)
    |> maybe_filter_by_distribution_types(distribution_types)
    |> Repo.all()
  end

  @spec get_total_sales_for_user(user_id :: Ecto.UUID.t() | String.t() | :ADMIN) ::
          %{
            sold_tickets: non_neg_integer(),
            sold_extras: non_neg_integer(),
            gross_sales: Decimal.t() | nil
          }
          | nil
  @spec get_total_sales_for_user(user_id :: Ecto.UUID.t() | String.t() | :ADMIN, opts :: keyword()) ::
          %{
            sold_tickets: non_neg_integer(),
            sold_extras: non_neg_integer(),
            gross_sales: Decimal.t() | nil
          }
          | nil
  def get_total_sales_for_user(user_id, opts \\ []) do
    admission = Keyword.get(opts, :admission)
    seller_id = Keyword.get(opts, :seller_id)

    query =
      user_id
      |> reportable_tickets_for_user_query(admission)
      |> where(
        [
          _ticket,
          _order_ticket,
          _order,
          event,
          _event_permission,
          _bill,
          _organizer
        ],
        event.is_draft == false
      )
      |> maybe_filter_by_seller_events(seller_id)
      |> select(
        [
          ticket,
          _order_ticket,
          _order,
          _event,
          _event_permission,
          bill,
          _organizer
        ],
        %{
          sold_tickets:
            fragment(
              "COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)",
              ticket.id,
              ticket.admission,
              true
            ),
          sold_extras:
            fragment(
              "COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)",
              ticket.id,
              ticket.admission,
              false
            ),
          gross_sales: sum(bill.organizer_total)
        }
      )

    Repo.one(query)
  end

  @spec get_total_sales_for_user(
          user_id :: Ecto.UUID.t() | String.t() | :ADMIN,
          from :: DateTime.t(),
          until :: DateTime.t()
        ) ::
          %{
            sold_tickets: non_neg_integer(),
            sold_extras: non_neg_integer(),
            gross_sales: Decimal.t() | nil
          }
          | nil
  @spec get_total_sales_for_user(
          user_id :: Ecto.UUID.t() | String.t() | :ADMIN,
          from :: DateTime.t(),
          until :: DateTime.t(),
          opts :: keyword()
        ) ::
          %{
            sold_tickets: non_neg_integer(),
            sold_extras: non_neg_integer(),
            gross_sales: Decimal.t() | nil
          }
          | nil
  def get_total_sales_for_user(user_id, from, until, opts \\ []) do
    admission = Keyword.get(opts, :admission)
    seller_id = Keyword.get(opts, :seller_id)

    query =
      user_id
      |> reportable_tickets_for_user_query(admission)
      |> where(
        [
          _ticket,
          _order_ticket,
          _order,
          event,
          _event_permission,
          _bill,
          _organizer
        ],
        event.is_draft == false
      )
      |> where(
        [
          ticket,
          _order_ticket,
          _order,
          _event,
          _event_permission,
          _bill,
          _organizer
        ],
        ticket.purchase_date >= ^from
      )
      |> where(
        [
          ticket,
          _order_ticket,
          _order,
          _event,
          _event_permission,
          _bill,
          _organizer
        ],
        ticket.purchase_date <= ^until
      )
      |> maybe_filter_by_seller_events(seller_id)
      |> select(
        [ticket, _order_ticket, _order, _event, _event_permission, bill],
        %{
          sold_tickets:
            fragment(
              "COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)",
              ticket.id,
              ticket.admission,
              true
            ),
          sold_extras:
            fragment(
              "COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)",
              ticket.id,
              ticket.admission,
              false
            ),
          gross_sales: sum(bill.organizer_total)
        }
      )

    Repo.one(query)
  end

  def get_total_sales_for_user_and_event(user_id, event_id, distribution_types, admission \\ nil) do
    query =
      user_id
      |> reportable_tickets_for_user_query(admission)
      |> join(
        :inner,
        [
          ticket: t,
          order_ticket: ot,
          order: o,
          event: e,
          event_permission: ep,
          bill: b,
          organizer: org
        ],
        variant in Variant,
        as: :variant,
        on: t.variant_id == variant.id and is_nil(variant.deleted_at)
      )
      |> where(
        [
          _ticket,
          _order_ticket,
          _order,
          event,
          _event_permission,
          _bill,
          _organizer
        ],
        event.id == ^event_id
      )
      |> maybe_filter_by_distribution_types(distribution_types)
      |> select(
        [ticket, _order_ticket, _order, _event, _event_permission, bill],
        %{
          sold_tickets:
            fragment(
              "COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)",
              ticket.id,
              ticket.admission,
              true
            ),
          sold_extras:
            fragment(
              "COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)",
              ticket.id,
              ticket.admission,
              false
            ),
          gross_sales: sum(bill.organizer_total)
        }
      )

    Repo.one(query)
  end

  def get_total_sales_for_user_and_event(user_id, event_id, from, until, distribution_types, admission \\ nil) do
    query =
      user_id
      |> reportable_tickets_for_user_query(admission)
      |> join(
        :inner,
        [
          ticket: t,
          order_ticket: ot,
          order: o,
          event: e,
          event_permission: ep,
          bill: b,
          organizer: org
        ],
        variant in Variant,
        as: :variant,
        on: t.variant_id == variant.id and is_nil(variant.deleted_at)
      )
      |> where(
        [
          _ticket,
          _order_ticket,
          _order,
          event,
          _event_permission,
          _bill,
          _organizer
        ],
        event.id == ^event_id
      )
      |> where(
        [
          ticket,
          _order_ticket,
          _order,
          _event,
          _event_permission,
          _bill,
          _organizer
        ],
        ticket.purchase_date >= ^from
      )
      |> where(
        [
          ticket,
          _order_ticket,
          _order,
          _event,
          _event_permission,
          _bill,
          _organizer
        ],
        ticket.purchase_date <= ^until
      )
      |> maybe_filter_by_distribution_types(distribution_types)
      |> select(
        [
          ticket,
          _order_ticket,
          _order,
          _event,
          _event_permission,
          bill,
          _organizer
        ],
        %{
          sold_tickets:
            fragment(
              "COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)",
              ticket.id,
              ticket.admission,
              true
            ),
          sold_extras:
            fragment(
              "COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)",
              ticket.id,
              ticket.admission,
              false
            ),
          sold_products: count(ticket.id),
          gross_sales: sum(bill.organizer_total)
        }
      )

    Repo.one(query)
  end

  def count_sold_tickets(event_id, "day", distribution_types) do
    event_id
    |> sold_tickets_base_query()
    |> maybe_filter_by_distribution_types(distribution_types)
    |> order_by([ticket], asc: fragment("?::date", ticket.purchase_date))
    |> group_by([ticket], fragment("?::date", ticket.purchase_date))
    |> select([ticket], %{
      period: fragment("?::date", ticket.purchase_date),
      count_tickets: count(ticket.id)
    })
    |> Repo.all()
  end

  def count_sold_tickets(event_id, "hour", distribution_types) do
    event_id
    |> sold_tickets_base_query()
    |> maybe_filter_by_distribution_types(distribution_types)
    |> order_by([ticket], asc: fragment("date_part('hour', ?)", ticket.purchase_date))
    |> group_by([ticket], fragment("date_part('hour', ?)", ticket.purchase_date))
    |> select([ticket], %{
      period: fragment("date_part('hour', ?)", ticket.purchase_date),
      count_tickets: count(ticket.id)
    })
    |> Repo.all()
  end

  def count_sold_tickets(event_id, "day_hour", distribution_types) do
    event_id
    |> sold_tickets_base_query()
    |> maybe_filter_by_distribution_types(distribution_types)
    |> order_by(
      [ticket],
      asc: fragment("extract(isodow from ?)", ticket.purchase_date),
      asc: fragment("date_part('hour', ?)", ticket.purchase_date)
    )
    |> group_by([ticket], fragment("extract(isodow from ?)", ticket.purchase_date))
    |> group_by([ticket], fragment("date_part('hour', ?)", ticket.purchase_date))
    |> select([ticket], %{
      day: fragment("extract(isodow from ?)", ticket.purchase_date),
      hour: fragment("date_part('hour', ?)", ticket.purchase_date),
      count_tickets: count(ticket.id)
    })
    |> Repo.all()
  end

  def count_sold_tickets(event_id, period, distribution_types, nil),
    do: count_sold_tickets(event_id, period, distribution_types)

  def count_sold_tickets(event_id, "day", distribution_types, admission) do
    event_id
    |> sold_tickets_base_query()
    |> maybe_filter_by_distribution_types(distribution_types)
    |> where([ticket], ticket.admission == ^admission)
    |> order_by([ticket], asc: fragment("?::date", ticket.purchase_date))
    |> group_by([ticket], fragment("?::date", ticket.purchase_date))
    |> select([ticket], %{
      period: fragment("?::date", ticket.purchase_date),
      count_tickets: count(ticket.id)
    })
    |> Repo.all()
  end

  def count_sold_tickets(event_id, "hour", distribution_types, admission) do
    event_id
    |> sold_tickets_base_query()
    |> maybe_filter_by_distribution_types(distribution_types)
    |> where([ticket], ticket.admission == ^admission)
    |> order_by([ticket], asc: fragment("date_part('hour', ?)", ticket.purchase_date))
    |> group_by([ticket], fragment("date_part('hour', ?)", ticket.purchase_date))
    |> select([ticket], %{
      period: fragment("date_part('hour', ?)", ticket.purchase_date),
      count_tickets: count(ticket.id)
    })
    |> Repo.all()
  end

  def count_sold_tickets(event_id, "day_hour", distribution_types, admission) do
    event_id
    |> sold_tickets_base_query()
    |> maybe_filter_by_distribution_types(distribution_types)
    |> where([ticket], ticket.admission == ^admission)
    |> order_by(
      [ticket],
      asc: fragment("extract(isodow from ?)", ticket.purchase_date),
      asc: fragment("date_part('hour', ?)", ticket.purchase_date)
    )
    |> group_by([ticket], fragment("extract(isodow from ?)", ticket.purchase_date))
    |> group_by([ticket], fragment("date_part('hour', ?)", ticket.purchase_date))
    |> select([ticket], %{
      day: fragment("extract(isodow from ?)", ticket.purchase_date),
      hour: fragment("date_part('hour', ?)", ticket.purchase_date),
      count_tickets: count(ticket.id)
    })
    |> Repo.all()
  end

  @doc """
  Returns total ticket counts for an event with filtering options.

  ## Parameters
  - event_id: The event ID to count tickets for
  - params: A map containing the following fields:
    - :status - List of specific ticket statuses to include (default: @reportable_ticket_status)

  ## Returns
  A map with:
  - :total_count - Total number of tickets
  - :admission_count - Number of admission tickets (admission=true)
  - :extras_count - Number of extra tickets (admission=false)
  """
  def count_total_tickets(event_id, params \\ %{}) do
    status_filter = Map.get(params, :status, @reportable_ticket_status)

    event_id
    |> tickets_base_query()
    |> where([ticket: t], t.status in ^status_filter)
    |> select([ticket: t], %{
      total_count: count(t.id),
      admission_count: filter(count(t.id), t.admission == true),
      extras_count: filter(count(t.id), t.admission == false)
    })
    |> Repo.one()
  end

  @spec ticket_status() :: [atom()]
  def ticket_status, do: @ticket_status

  def get_distinct_order_ids_by_event_id(event_id) do
    from(ticket in Ticket,
      inner_join: ot in OrderTicket,
      on: ticket.id == ot.ticket_id,
      where: ticket.event_id == ^event_id,
      distinct: ot.order_id,
      select: ot.order_id
    )
  end

  @spec get_reportable_by_organizer_id(
          organizer_id :: Ecto.UUID.t(),
          start_date :: DateTime.t(),
          end_date :: DateTime.t(),
          preloads :: [atom()]
        ) :: [map()]
  def get_reportable_by_organizer_id(organizer_id, start_date, end_date, preloads \\ []) do
    query =
      from(t in Ticket,
        as: :ticket,
        left_join: ot in OrderTicket,
        on: t.id == ot.ticket_id,
        as: :order_ticket,
        left_join: o in Order,
        on: ot.order_id == o.id,
        as: :order,
        left_join: e in Event,
        on: t.event_id == e.id,
        as: :event,
        where: e.promoter_id == ^organizer_id,
        where: t.status != :REFUNDED,
        where: t.purchase_date > ^start_date and t.purchase_date < ^end_date,
        where: o.status == :PAID or is_nil(o.id),
        preload: ^preloads
      )

    Repo.all(query)
  end

  @spec find_date_for_status(map(), atom()) :: DateTime.t() | nil
  def find_date_for_status(%Ticket{status: :ACTIVE}, :USED), do: nil
  def find_date_for_status(%Ticket{status: :UNUSED}, :USED), do: nil

  def find_date_for_status(%Ticket{ticket_history: ticket_history}, status) when is_list(ticket_history) do
    Enum.find_value(ticket_history, fn history ->
      if history.status_after == status, do: history.inserted_at
    end)
  end

  def find_date_for_status(_ticket, _status), do: nil

  defp reportable_tickets_for_user_query(:ADMIN) do
    from(
      ticket in Ticket,
      as: :ticket,
      inner_join: ot in OrderTicket,
      as: :order_ticket,
      on: ticket.id == ot.ticket_id,
      inner_join: order in Order,
      as: :order,
      on: ot.order_id == order.id,
      inner_join: event in Event,
      as: :event,
      on: ticket.event_id == event.id,
      inner_join: event_permission in EventPermission,
      as: :event_permission,
      on: ticket.event_id == event_permission.event_id,
      inner_join: bill in Bill,
      as: :bill,
      on: ot.bill_id == bill.id,
      inner_join: organizer in Organizer,
      as: :organizer,
      on: event.promoter_id == organizer.id,
      where: order.status == ^:PAID,
      where: ticket.status in ^@reportable_ticket_status,
      where: event_permission.user_document_id == organizer.created_by_document_id,
      where: ticket.distribution_type != :GUEST_LIST_INVITATION,
      where:
        ^:ADMIN in event_permission.role or ^:EVENT_ADMIN in event_permission.role or
          ^:PROMOTER in event_permission.role
    )
  end

  defp reportable_tickets_for_user_query(user_id) do
    user_id_field =
      case Ecto.UUID.cast(user_id) do
        {:ok, _} -> :user_id
        :error -> :user_document_id
      end

    from(ticket in Ticket,
      as: :ticket,
      inner_join: ot in OrderTicket,
      as: :order_ticket,
      on: ticket.id == ot.ticket_id,
      inner_join: order in Order,
      as: :order,
      on: ot.order_id == order.id,
      inner_join: event in Event,
      as: :event,
      on: ticket.event_id == event.id,
      inner_join: event_permission in EventPermission,
      as: :event_permission,
      on: ticket.event_id == event_permission.event_id,
      inner_join: bill in Bill,
      as: :bill,
      on: ot.bill_id == bill.id,
      inner_join: organizer in Organizer,
      as: :organizer,
      on: event.promoter_id == organizer.id,
      where: order.status == ^:PAID,
      where: ticket.status in ^@reportable_ticket_status,
      where: field(event_permission, ^user_id_field) == ^user_id,
      where: ticket.distribution_type != :GUEST_LIST_INVITATION,
      where:
        ^:ADMIN in event_permission.role or ^:EVENT_ADMIN in event_permission.role or
          ^:PROMOTER in event_permission.role
    )
  end

  defp reportable_tickets_for_user_query(user_id, nil), do: reportable_tickets_for_user_query(user_id)

  defp reportable_tickets_for_user_query(user_id, admission) do
    user_id
    |> reportable_tickets_for_user_query()
    |> where([ticket], ticket.admission == ^admission)
  end

  defp get_sold_tickets_for_event_group_by_variant_query(event_id) do
    from(ticket in Ticket,
      as: :ticket,
      inner_join: ot in OrderTicket,
      as: :order_ticket,
      on: ticket.id == ot.ticket_id,
      inner_join: order in Order,
      as: :order,
      on: ot.order_id == order.id,
      inner_join: event in Event,
      as: :event,
      on: ticket.event_id == event.id,
      inner_join: variant in Variant,
      as: :variant,
      on: ticket.variant_id == variant.id,
      left_join: multi_level_pricing_modifier_variant in MultiLevelPricingModifierVariant,
      as: :mlpm_variant,
      on: variant.id == multi_level_pricing_modifier_variant.variant_id,
      left_join: multi_level_pricing_modifier in MultiLevelPricingModifier,
      as: :mlpm,
      on: multi_level_pricing_modifier_variant.mlpm_id == multi_level_pricing_modifier.id,
      inner_join: category in TicketCategory,
      as: :category,
      on: ticket.category_id == category.id,
      where: order.status == ^:PAID,
      where: event.id == ^event_id,
      where: ticket.status in ^@reportable_ticket_status,
      where: ticket.distribution_type != :GUEST_LIST_INVITATION,
      group_by: [
        variant.id,
        category.id,
        category.name,
        variant.quota,
        category.admission,
        multi_level_pricing_modifier.id
      ],
      select: %{
        variant_id: variant.id,
        category_id: category.id,
        category_name: category.name,
        mlpm_label: multi_level_pricing_modifier.label,
        mlpm_id: multi_level_pricing_modifier.id,
        sold_tickets: fragment("COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)", ticket.id, ticket.admission, true),
        sold_extras: fragment("COUNT (DISTINCT(?)) FILTER (WHERE ? = ?)", ticket.id, ticket.admission, false),
        sold_products: count(ticket.id),
        quota: variant.quota,
        admission: category.admission,
        unit_price: variant.unit_price,
        distribution_type: variant.distribution_type
      }
    )
  end

  defp maybe_filter_by_admission(query, nil), do: query

  defp maybe_filter_by_admission(query, admission), do: where(query, [ticket: t], t.admission == ^admission)

  defp maybe_filter_by_distribution_types(query, nil), do: query
  defp maybe_filter_by_distribution_types(query, []), do: query

  defp maybe_filter_by_distribution_types(query, distribution_types),
    do: where(query, [variant: v], v.distribution_type in ^distribution_types)

  defp sold_tickets_base_query(event_id) do
    from(
      ticket in Ticket,
      as: :ticket,
      inner_join: ot in OrderTicket,
      as: :order_ticket,
      on: ticket.id == ot.ticket_id,
      inner_join: order in Order,
      as: :order,
      on: ot.order_id == order.id,
      inner_join: event in Event,
      as: :event,
      on: ticket.event_id == event.id,
      inner_join: variant in Variant,
      as: :variant,
      on: ticket.variant_id == variant.id,
      where: order.status == ^:PAID,
      where: event.id == ^event_id,
      where: ticket.status in ^@reportable_ticket_status,
      where: ticket.distribution_type != :GUEST_LIST_INVITATION
    )
  end

  defp tickets_base_query(event_id) do
    from(
      ticket in Ticket,
      as: :ticket,
      where: ticket.event_id == ^event_id
    )
  end

  # Helper function to get event IDs for organizer seller
  # Only returns events where the seller is an ORGANIZER and its promoter's
  # created_by_document_id has EventPermission (EVENT_ADMIN or SECURITY role)
  # Base query that both functions can use
  defp get_organizer_event_ids_query(seller_id) do
    seller_id
    |> SellerPermissions.base_permission_query()
    |> select([event_permission: ep], ep.event_id)
  end

  defp maybe_filter_by_seller_events(query, nil), do: query

  defp maybe_filter_by_seller_events(query, seller_id) do
    organizer_event_ids = get_organizer_event_ids_query(seller_id)

    where(query, [event: e], e.id in subquery(organizer_event_ids))
  end
end
