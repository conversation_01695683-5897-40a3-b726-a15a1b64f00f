defmodule ReportsService.Orders.Bill do
  @moduledoc false

  use Ecto.Schema

  # styler:sort
  @type t ::
          %__MODULE__{
            id: Ecto.UUID.t(),
            total: integer(),
            discount: integer(),
            donation: integer(),
            fee: integer(),
            presale_fee: integer(),
            presale_fee_tax: integer(),
            organizer_total: integer(),
            promoter_net_total: integer(),
            promoter_tax: integer(),
            future_demand_fee: integer(),
            future_demand_fee_tax: integer(),
            system_fee: integer(),
            system_fee_tax: integer(),
            shipping_fee: integer(),
            shipping_fee_tax: integer(),
            promoter_kickback: integer(),
            promoter_kickback_tax: integer(),
            promoter_tax_rate: float(),
            payment_method_fee: integer(),
            payment_method_fee_tax: integer(),
            stagedates_tax_rate: float(),
            inserted_at: DateTime.t(),
            updated_at: DateTime.t()
          }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  @schema_prefix "orders"
  # styler:sort
  schema "bills" do
    field :discount, :integer
    field :donation, :integer
    field :fee, :integer
    field :future_demand_fee, :integer
    field :future_demand_fee_tax, :integer
    # rename to organizer_total can only be made in orders-service
    field :organizer_total, :integer, source: :promoter_total
    field :payment_method_fee, :integer
    field :payment_method_fee_tax, :integer

    field :presale_fee, :integer
    field :presale_fee_tax, :integer
    field :promoter_kickback, :integer
    field :promoter_kickback_tax, :integer
    field :promoter_net_total, :integer
    field :promoter_tax, :integer
    field :promoter_tax_rate, :float
    field :shipping_fee, :integer
    field :shipping_fee_tax, :integer
    field :stagedates_tax_rate, :float
    field :system_fee, :integer
    field :system_fee_tax, :integer
    field :total, :integer
    timestamps()
  end
end
