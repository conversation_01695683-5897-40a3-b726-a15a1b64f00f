defmodule ReportsService.Orders.Address do
  @moduledoc false

  use Ecto.Schema

  # styler:sort
  @type t ::
          %__MODULE__{
            company: String.t(),
            deleted_at: DateTime.t(),
            formatted: String.t(),
            id: Ecto.UUID.t(),
            inserted_at: DateTime.t(),
            is_business: boolean(),
            locality: String.t(),
            postal_code: String.t(),
            region: String.t(),
            street_address: String.t(),
            updated_at: DateTime.t(),
            vat_id: String.t()
          }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  @schema_prefix "orders"
  # styler:sort
  schema "addresses" do
    field :company, :string
    field :deleted_at, :utc_datetime

    # https://openid.net/specs/openid-connect-basic-1_0.html#AddressClaim
    field :formatted, :string
    field :is_business, :boolean, default: false
    field :locality, :string
    field :postal_code, :string
    field :region, :string
    field :street_address, :string
    field :vat_id, :string

    timestamps()
  end
end
