defmodule ReportsService.Orders.TicketExporter do
  @moduledoc false

  use Gettext, backend: ReportsServiceWeb.Gettext

  alias ReportsService.Orders.Ticket
  alias ReportsService.Util.MoneyFormatter

  require Logger

  @spec build_exportable_tickets_info(tickets :: [Ticket.t()], events :: [map()], vouchers :: [map()]) :: [map()]
  def build_exportable_tickets_info(tickets, events, vouchers) do
    event_map = create_event_map(events)
    voucher_map = create_voucher_map(vouchers)
    variant_name_map = create_variant_name_map(events)
    sales_channel_name_map = create_sales_channel_name_map(events)
    mlpm_name_map = create_mlpm_name_map(events)

    for ticket <- tickets, ticket.status != :SWAPPED do
      ticket
      |> maybe_substitute_swapped_ticket_info_with_parent(tickets)
      |> build_exportable_ticket_info(
        event_map[ticket.event_id],
        voucher_map[ticket.voucher_id],
        variant_name_map,
        sales_channel_name_map,
        mlpm_name_map
      )
    end
  end

  @spec build_exportable_ticket_info(
          ticket :: Ticket.t(),
          event :: map(),
          voucher :: map(),
          variant_name_map :: map(),
          sales_channel_name_map :: map(),
          mlpm_name_map :: map()
        ) :: map()
  def build_exportable_ticket_info(ticket, event, voucher, variant_name_map, sales_channel_name_map, mlpm_name_map) do
    bill = (ticket.order_ticket && ticket.order_ticket.bill) || nil

    # styler:sort
    %{
      birthdate: get_in(ticket.owner.birthdate),
      check_in_date: Ticket.find_date_for_status(ticket, :USED),
      city: get_city(ticket),
      company: get_company(ticket),
      delivery_type: ticket.type,
      event_id: ticket.event_id,
      event_title: event.title,
      family_name: get_family_name(ticket),
      future_demand_fee: calculate_total(get_in(bill.future_demand_fee), get_in(bill.future_demand_fee_tax)),
      given_name: get_given_name(ticket),
      guestlist: get_in(bill.organizer_total) == 0,
      hard_ticket_fee: get_hard_ticket_fee(ticket),
      kickback: calculate_total(get_in(bill.promoter_kickback), get_in(bill.promoter_kickback_tax)),
      order_email: (ticket.order_ticket && ticket.order_ticket.order.email) || nil,
      order_id: get_in(ticket.order_ticket.order_id),
      payment_method: get_payment_method(ticket),
      postal_code: get_postal_code(ticket),
      presale_fee: calculate_total(get_in(bill.presale_fee), get_in(bill.presale_fee_tax)),
      price_level_name: mlpm_name_map[ticket.variant_id],
      product: variant_name_map[ticket.variant_id],
      product_type: get_product_type(ticket),
      purchase_date: ticket.purchase_date,
      sales_channel: sales_channel_name_map[ticket.variant_id],
      street: get_street(ticket),
      system_fee: calculate_total(get_in(bill.system_fee), get_in(bill.system_fee_tax)),
      ticket_id: ticket.id,
      ticket_price: MoneyFormatter.format_cents(get_in(bill.organizer_total)),
      ticket_swap: not is_nil(ticket.swap_transaction),
      total_price: MoneyFormatter.format_cents(get_in(bill.total)),
      voucher_code: get_in(voucher.code),
      voucher_description: get_in(voucher.description),
      voucher_value: get_voucher_value(voucher)
    }
  end

  @spec create_event_map(events :: [map()]) :: map()
  def create_event_map(events) do
    Map.new(events, fn event -> {event.id, event} end)
  end

  @spec create_voucher_map(vouchers :: [map()]) :: map()
  def create_voucher_map(vouchers) do
    Map.new(vouchers, fn voucher -> {voucher.id, voucher} end)
  end

  @spec create_variant_name_map(events :: [map()]) :: map()
  def create_variant_name_map(events) do
    events
    |> Enum.flat_map(fn
      %{variants: variants} -> variants
      _ -> []
    end)
    |> Map.new(fn variant -> {variant.id, variant.ticket_category.name} end)
  end

  @spec create_sales_channel_name_map(events :: [map()]) :: map()
  def create_sales_channel_name_map(events) do
    events
    |> Enum.flat_map(fn
      %{variants: variants} -> variants
      _ -> []
    end)
    |> Enum.filter(fn variant -> not is_nil(variant.sales_channel) end)
    |> Map.new(fn variant -> {variant.id, variant.sales_channel.channel_config.label} end)
  end

  @spec create_mlpm_name_map(events :: [map()]) :: map()
  def create_mlpm_name_map(events) do
    events
    |> Enum.flat_map(fn
      %{variants: variants} -> variants
      _ -> []
    end)
    |> Enum.filter(fn variant -> not is_nil(variant.mlpm) and length(variant.mlpm) > 0 end)
    |> Map.new(fn variant -> {variant.id, List.first(variant.mlpm).label} end)
  end

  defp get_hard_ticket_fee(%{type: "HARD"} = ticket) do
    calculate_total(ticket.order_ticket.bill.shipping_fee, ticket.order_ticket.bill.shipping_fee_tax)
  end

  defp get_hard_ticket_fee(_ticket) do
    MoneyFormatter.format_cents(nil)
  end

  defp get_voucher_value(%{value: value}), do: "#{value}%"
  defp get_voucher_value(_voucher), do: nil

  defp get_payment_method(%{order_ticket: %{order: %{payin_transaction: %{payment_method: payment_method}}}}),
    do: payment_method

  defp get_payment_method(_ticket), do: nil

  defp get_company(%{order_ticket: %{order: %{billing_address: %{company: company}}}}), do: company
  defp get_company(_address), do: nil

  defp get_street(%{order_ticket: %{order: %{billing_address: %{street_address: street_address}}}}), do: street_address
  defp get_street(_address), do: nil

  defp get_postal_code(%{order_ticket: %{order: %{billing_address: %{postal_code: postal_code}}}}), do: postal_code

  defp get_postal_code(%{order_ticket: %{order: %{created_by_personal_information: %{postal_code: postal_code}}}}),
    do: postal_code

  defp get_postal_code(_address), do: nil

  defp get_city(%{order_ticket: %{order: %{billing_address: %{locality: locality}}}}), do: locality
  defp get_city(%{order_ticket: %{order: %{created_by_personal_information: %{city: city}}}}), do: city
  defp get_city(_address), do: nil

  defp get_given_name(%{order_ticket: %{order: %{created_by_personal_information: %{given_name: given_name}}}}),
    do: given_name

  defp get_given_name(_personal_info), do: nil

  defp get_family_name(%{order_ticket: %{order: %{created_by_personal_information: %{family_name: family_name}}}}),
    do: family_name

  defp get_family_name(_personal_info), do: nil

  defp calculate_total(tax, fee) when is_number(tax) and is_number(fee) do
    MoneyFormatter.format_cents(tax + fee)
  end

  defp calculate_total(tax, _fee) when is_number(tax), do: MoneyFormatter.format_cents(tax)

  defp calculate_total(_tax, _fee), do: MoneyFormatter.format_cents(nil)

  defp get_product_type(%{admission: false} = _ticket), do: :extra

  defp get_product_type(_ticket), do: :ticket

  defp maybe_substitute_swapped_ticket_info_with_parent(%{swap_transaction: nil} = ticket, _tickets), do: ticket

  defp maybe_substitute_swapped_ticket_info_with_parent(%{swap_transaction: swap_transaction} = ticket, tickets) do
    parent_ticket = Enum.find(tickets, &(&1.id == swap_transaction.parent_ticket_id))

    substitute_order_ticket_properties(ticket, parent_ticket)
  end

  defp substitute_order_ticket_properties(ticket, parent_ticket) do
    put_in(ticket.order_ticket, %{
      bill: maybe_get_bill(parent_ticket),
      order: %{
        created_by_personal_information: %{
          given_name: maybe_get_given_name(ticket),
          family_name: maybe_get_family_name(ticket)
        },
        email: maybe_get_email(ticket)
      },
      order_id: nil
    })
  end

  defp maybe_get_bill(%{order_ticket: %{bill: bill}} = _parent_ticket), do: bill

  defp maybe_get_bill(_parent_ticket), do: %{}

  defp maybe_get_email(%{attendee: %{email: email}} = _ticket), do: email

  defp maybe_get_email(_ticket), do: nil

  defp maybe_get_given_name(%{attendee: %{given_name: given_name}} = _ticket), do: given_name

  defp maybe_get_given_name(_ticket), do: nil

  defp maybe_get_family_name(%{attendee: %{family_name: family_name}} = _ticket), do: family_name

  defp maybe_get_family_name(_ticket), do: nil
end
