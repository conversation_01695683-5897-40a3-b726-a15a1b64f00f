defmodule ReportsService.Orders.ImportedOrder do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query

  alias Money.Ecto.Composite.Type
  alias ReportsService.Orders.ImportedOrder
  alias ReportsService.Repo

  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          imported_event_id: Ecto.UUID.t(),
          external_order_id: String.t(),
          amount: integer(),
          ticket_price: Money.t(),
          system_fee: Money.t(),
          presale_fee: Money.t(),
          kickback: Money.t(),
          other_fees: Money.t(),
          total_price: Money.t(),
          tax_rate: Decimal.t(),
          deleted_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "imported_orders" do
    field :imported_event_id, Ecto.UUID
    field :external_order_id, :string
    field :order_date, :utc_datetime
    field :amount, :integer
    field :ticket_price, Type
    field :system_fee, Type
    field :presale_fee, Type
    field :kickback, Type
    field :other_fees, Type
    field :total_price, Type
    field :tax_rate, :decimal
    field :deleted_at, :utc_datetime

    timestamps(type: :utc_datetime)
  end

  def changeset(imported_order, attrs) do
    imported_order
    |> cast(attrs, [
      :imported_event_id,
      :external_order_id,
      :order_date,
      :amount,
      :ticket_price,
      :system_fee,
      :presale_fee,
      :kickback,
      :other_fees,
      :total_price,
      :tax_rate,
      :deleted_at
    ])
    |> validate_required([:imported_event_id, :order_date, :amount])
  end

  @spec get_by_imported_event_id(imported_event_id :: Ecto.UUID.t()) :: ImportedOrder.t() | nil
  def get_by_imported_event_id(imported_event_id) do
    Repo.get_by(ImportedOrder, imported_event_id: imported_event_id)
  end

  @spec count_sold_tickets_by_imported_event_id(imported_event_id :: Ecto.UUID.t()) :: [map()]
  def count_sold_tickets_by_imported_event_id(imported_event_id) do
    query =
      from(io in ImportedOrder,
        where: io.imported_event_id == ^imported_event_id,
        group_by: fragment("?::date", io.order_date),
        select: %{
          period: fragment("?::date", io.order_date),
          count_tickets: sum(io.amount)
        }
      )

    Repo.all(query)
  end
end
