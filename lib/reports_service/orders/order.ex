defmodule ReportsService.Orders.Order do
  @moduledoc false

  use Ecto.Schema

  import Ecto.Query

  alias ReportsService.Orders.Address
  alias ReportsService.Orders.Order
  alias ReportsService.Orders.PayinTransaction
  alias ReportsService.Orders.PersonalInformation
  alias ReportsService.Orders.Ticket
  alias ReportsService.Repo

  require Logger

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  @schema_prefix "orders"
  # styler:sort
  schema "orders" do
    belongs_to :billing_address, Address, foreign_key: :billing_address_id
    belongs_to :created_by_personal_information, PersonalInformation, foreign_key: :created_by
    field :bill_id, :binary_id
    field :deleted_at, :utc_datetime

    field :email, :string

    field :status, Ecto.Enum,
      values: [
        :AWAITING_PAYMENT,
        :CREATED,
        :DEFRAUDED,
        :FAILED,
        :PENDING,
        :PAID,
        :REFUND_PENDING,
        :REFUNDED,
        :TIMEDOUT
      ]

    has_one :payin_transaction, PayinTransaction

    timestamps()
  end

  def get_demographics(event_id) do
    Repo.one(
      from(order in Order,
        left_join: personal_information in PersonalInformation,
        on: order.created_by == personal_information.id,
        where: order.id in subquery(Ticket.get_distinct_order_ids_by_event_id(event_id)) and order.status == ^:PAID,
        where:
          fragment("DATE_PART('year', AGE(CURRENT_DATE, ?)) >= 0", personal_information.birthdate) and
            fragment("DATE_PART('year', AGE(CURRENT_DATE, ?)) <= 99", personal_information.birthdate),
        select: %{
          min_age: min(fragment("DATE_PART('year', AGE(CURRENT_DATE, ?))", personal_information.birthdate)),
          max_age: max(fragment("DATE_PART('year', AGE(CURRENT_DATE, ?))", personal_information.birthdate)),
          average_age: avg(fragment("DATE_PART('year', AGE(CURRENT_DATE, ?))", personal_information.birthdate))
        }
      )
    )
  end

  def count_order_by_event_id(event_id) do
    Repo.one(
      from(order in Order,
        where: order.id in subquery(Ticket.get_distinct_order_ids_by_event_id(event_id)) and order.status == ^:PAID,
        select: count(order.id)
      )
    )
  end

  def get_paid_orders_report(event_id, nil, _filter_params), do: {:ok, count_order_by_event_id(event_id)}

  def get_paid_orders_report(event_id, :gender_age, _filter_params) do
    query =
      from(order in Order,
        left_join: personal_information in PersonalInformation,
        on: order.created_by == personal_information.id,
        where: order.id in subquery(Ticket.get_distinct_order_ids_by_event_id(event_id)) and order.status == ^:PAID,
        where: not is_nil(personal_information.gender),
        group_by: [
          personal_information.gender,
          fragment(
            "CASE
              WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
              WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
              WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
              WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
              WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
              WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
              WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
              WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
              ELSE 'not specified'
            END",
            personal_information.birthdate,
            0,
            17,
            "0-17",
            personal_information.birthdate,
            18,
            21,
            "18-21",
            personal_information.birthdate,
            22,
            29,
            "22-29",
            personal_information.birthdate,
            30,
            39,
            "30-39",
            personal_information.birthdate,
            40,
            49,
            "40-49",
            personal_information.birthdate,
            50,
            59,
            "50-59",
            personal_information.birthdate,
            60,
            69,
            "60-69",
            personal_information.birthdate,
            70,
            99,
            "70+"
          )
        ],
        select: %{
          gender: personal_information.gender,
          age_range:
            fragment(
              "CASE
                WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
                WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
                WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
                WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
                WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
                WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
                WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
                WHEN DATE_PART('year', AGE(CURRENT_DATE, ?)) BETWEEN ? AND ? THEN ?
                ELSE 'not specified'
              END",
              personal_information.birthdate,
              0,
              17,
              "0-17",
              personal_information.birthdate,
              18,
              21,
              "18-21",
              personal_information.birthdate,
              22,
              29,
              "22-29",
              personal_information.birthdate,
              30,
              39,
              "30-39",
              personal_information.birthdate,
              40,
              49,
              "40-49",
              personal_information.birthdate,
              50,
              59,
              "50-59",
              personal_information.birthdate,
              60,
              69,
              "60-69",
              personal_information.birthdate,
              70,
              99,
              "70+"
            ),
          amount: count(order.id),
          percentage:
            fragment("CASE  WHEN COUNT(?) = 0 THEN 0 ELSE COUNT(?) * 100 / SUM(COUNT(*)) OVER () END", order.id, order.id)
        }
      )

    actual_data = Repo.all(query)
    age_groups = ["not specified", "0-17", "18-21", "22-29", "30-39", "40-49", "50-59", "60-69", "70+"]
    genders = [:"1", :"2", :"9"]
    # Define all possible gender and age group combinations
    all_combinations =
      for gender <- genders, age <- age_groups do
        %{gender: gender, age_range: age, amount: 0, percentage: 0}
      end

    # Merge actual data with all combinations, ensuring all combinations are present
    result =
      Enum.map(all_combinations, fn combination ->
        Map.merge(
          combination,
          Enum.find(actual_data, fn data ->
            data.gender == combination.gender and data.age_range == combination.age_range
          end) || %{}
        )
      end)

    {:ok, result}
  end

  def get_paid_orders_report(event_id, :gender, _filter_params) do
    return =
      Repo.all(
        from(order in Order,
          inner_join: personal_information in PersonalInformation,
          on: order.created_by == personal_information.id,
          where: order.id in subquery(Ticket.get_distinct_order_ids_by_event_id(event_id)) and order.status == ^:PAID,
          where: not is_nil(personal_information.gender),
          group_by: [personal_information.gender],
          select: %{
            gender: personal_information.gender,
            amount: count(order.id),
            percentage:
              fragment(
                "CASE  WHEN COUNT(?) = 0 THEN 0 ELSE COUNT(?) * 100 / SUM(COUNT(*)) OVER () END",
                order.id,
                order.id
              )
          }
        )
      )

    {:ok, return}
  end

  def get_paid_orders_report(event_id, :location, filter_params) do
    case paid_orders_report_raw_query(event_id, :location, filter_params) do
      {:ok, result} ->
        return =
          Enum.map(result.rows, fn [postal_code, geo_latitude, geo_longitude, count] ->
            %{postal_code: postal_code, lat: geo_latitude, lon: geo_longitude, amount: count}
          end)

        {:ok, return}

      {:error, error} ->
        Logger.error("Can't count order for event #{inspect(event_id)} grouped by location because of #{inspect(error)}")
        {:error, error}
    end
  end

  def get_paid_orders_report(event_id, group_by, _filter_params) do
    Logger.error("Can't report orders for event #{inspect(event_id)} group by #{inspect(group_by)}. It's not implemented")
    {:error, :unknown_parameter}
  end

  def paid_orders_report_raw_query(event_id, :location, %{from: nil, to: nil}) do
    raw_sql =
      """
       select pc.postal_code, pc.geo_latitude, pc.geo_longitude, count (o.id) 
         from orders.orders o 
         inner join orders.personal_informations pis on o.created_by = pis.id 
         inner join orders.countries c on pis.country_iso = c.iso
         join lateral(
            select pc.postal_code, pc.geo_latitude, pc.geo_longitude, similarity(pc.city, pis.city)
            from reports.postal_codes pc 
            where pc.postal_code = pis.postal_code 
              and pc.country_iso = c.iso 
            order by similarity(pc.city, pis.city) desc limit 1) pc on true 
         where o.id in (select distinct ot.order_id from orders.tickets t inner join orders.order_tickets ot on t.id = ot.ticket_id where t.event_id::text = $1)
           and o.id not in (select distinct io.order_id from orders.invitation_orders io)
           and o.status::text = 'PAID'
           and o.deleted_at is null
         group by pc.postal_code, pc.geo_latitude, pc.geo_longitude;
      """

    Repo.query(raw_sql, [event_id])
  end

  def paid_orders_report_raw_query(event_id, :location, %{from: nil, to: to}) do
    raw_sql = """
     select pc.postal_code, pc.geo_latitude, pc.geo_longitude, count (o.id) 
       from orders.orders o 
       inner join orders.personal_informations pis on o.created_by = pis.id 
       inner join orders.countries c on pis.country_iso = c.iso
       join lateral(
          select pc.postal_code, pc.geo_latitude, pc.geo_longitude, similarity(pc.city, pis.city)
          from reports.postal_codes pc 
          where pc.postal_code = pis.postal_code 
            and pc.country_iso = c.iso 
          order by similarity(pc.city, pis.city) desc limit 1) pc on true 
       where o.id in (select distinct ot.order_id from orders.tickets t inner join orders.order_tickets ot on t.id = ot.ticket_id where t.event_id::text = $1)
         and o.id not in (select distinct io.order_id from orders.invitation_orders io)
         and o.status::text = 'PAID'
         and o.deleted_at is null
         and o.inserted_at <= $2
       group by pc.postal_code, pc.geo_latitude, pc.geo_longitude;
    """

    Repo.query(raw_sql, [event_id, to])
  end

  def paid_orders_report_raw_query(event_id, :location, %{from: from, to: nil}) do
    raw_sql = """
     select pc.postal_code, pc.geo_latitude, pc.geo_longitude, count (o.id) 
       from orders.orders o 
       inner join orders.personal_informations pis on o.created_by = pis.id 
       inner join orders.countries c on pis.country_iso = c.iso
       join lateral(
          select pc.postal_code, pc.geo_latitude, pc.geo_longitude, similarity(pc.city, pis.city)
          from reports.postal_codes pc 
          where pc.postal_code = pis.postal_code 
            and pc.country_iso = c.iso 
          order by similarity(pc.city, pis.city) desc limit 1) pc on true 
       where o.id in (select distinct ot.order_id from orders.tickets t inner join orders.order_tickets ot on t.id = ot.ticket_id where t.event_id::text = $1)
         and o.id not in (select distinct io.order_id from orders.invitation_orders io)
         and o.status::text = 'PAID'
         and o.deleted_at is null
         and o.inserted_at >= $2
       group by pc.postal_code, pc.geo_latitude, pc.geo_longitude;
    """

    Repo.query(raw_sql, [event_id, from])
  end

  def paid_orders_report_raw_query(event_id, :location, %{from: from, to: to}) do
    raw_sql = """
     select pc.postal_code, pc.geo_latitude, pc.geo_longitude, count (o.id) 
       from orders.orders o 
       inner join orders.personal_informations pis on o.created_by = pis.id 
       inner join orders.countries c on pis.country_iso = c.iso
       join lateral(
          select pc.postal_code, pc.geo_latitude, pc.geo_longitude, similarity(pc.city, pis.city)
          from reports.postal_codes pc 
          where pc.postal_code = pis.postal_code 
            and pc.country_iso = c.iso 
          order by similarity(pc.city, pis.city) desc limit 1) pc on true 
       where o.id in (select distinct ot.order_id from orders.tickets t inner join orders.order_tickets ot on t.id = ot.ticket_id where t.event_id::text = $1)
         and o.id not in (select distinct io.order_id from orders.invitation_orders io)
         and o.status::text = 'PAID'
         and o.deleted_at is null
         and o.inserted_at between $2 and $3
       group by pc.postal_code, pc.geo_latitude, pc.geo_longitude;
    """

    Repo.query(raw_sql, [event_id, from, to])
  end

  def get_ticket_buyer_city(event_id) do
    Repo.all(
      from(order in Order,
        left_join: personal_information in PersonalInformation,
        on: order.created_by == personal_information.id,
        where: order.id in subquery(Ticket.get_distinct_order_ids_by_event_id(event_id)) and order.status == ^:PAID,
        group_by: [personal_information.city],
        select: %{
          amount: count(order.id),
          city:
            fragment(
              "CASE  WHEN ? is null THEN 'not specified' ELSE ? END",
              personal_information.city,
              personal_information.city
            )
        }
      )
    )
  end
end
