defmodule ReportsService.Orders.PayinTransaction do
  @moduledoc false

  use Ecto.Schema

  alias ReportsService.Orders.Order

  @primary_key {:id, :binary_id, autogenerate: true}
  @schema_prefix "orders"
  @foreign_key_type :binary_id
  # styler:sort
  schema "payin_transactions" do
    belongs_to(:order, Order, foreign_key: :order_id)

    field :amount, :integer
    field :currency, :string
    field :payment_method, :string
    field :psp, :string
    field :psp_reference, :string
    field :psp_result, :map

    field :status, :string
    timestamps()
  end

  # styler:sort
  @type t :: %__MODULE__{
          amount: integer(),
          currency: String.t(),
          id: binary(),
          order_id: Ecto.UUID.t(),
          payment_method: String.t(),
          psp: String.t(),
          psp_reference: String.t(),
          psp_result: map(),
          status: String.t(),
          inserted_at: NaiveDateTime.t(),
          updated_at: NaiveDateTime.t()
        }
end
