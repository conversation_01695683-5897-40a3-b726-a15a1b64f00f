defmodule ReportsService.Orders.SwapTransaction do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          attendee_id: Ecto.UUID.t(),
          parent_order_id: Ecto.UUID.t(),
          parent_ticket_id: Ecto.UUID.t(),
          original_order_id: Ecto.UUID.t(),
          original_ticket_id: Ecto.UUID.t(),
          new_ticket_id: Ecto.UUID.t(),
          inserted_at: DateTime.t()
        }
  # styler:sort
  schema "swap_transactions" do
    field :attendee_id, Ecto.UUID
    field :new_ticket_id, Ecto.UUID

    field :original_order_id, Ecto.UUID
    field :original_ticket_id, Ecto.UUID
    field :parent_order_id, Ecto.UUID
    field :parent_ticket_id, Ecto.UUID
    timestamps(updated_at: false)
  end

  @doc false
  def changeset(swap_transaction, attrs) do
    swap_transaction
    |> cast(attrs, [
      :attendee_id,
      :parent_order_id,
      :parent_ticket_id,
      :original_order_id,
      :original_ticket_id,
      :new_ticket_id
    ])
    |> validate_required([
      :attendee_id,
      :parent_order_id,
      :parent_ticket_id,
      :original_order_id,
      :original_ticket_id,
      :new_ticket_id
    ])
  end
end
