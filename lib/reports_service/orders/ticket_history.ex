defmodule ReportsService.Orders.TicketHistory do
  @moduledoc false
  use Ecto.Schema

  alias ReportsService.Orders.Ticket

  @actor_types [
    :USER,
    :SYSTEM
  ]

  @ticket_status [
    :ACTIVE,
    :CREATED,
    :DEFRAUDED,
    :INVITATION_REJECTED,
    :PENDING,
    :REFUNDED,
    :TIMEDOUT,
    :USED,
    :UNUSED
  ]

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  @schema_prefix "orders"
  schema "ticket_histories" do
    field :metadata, :map
    field :actor_id, Ecto.UUID
    field :location_id, Ecto.UUID
    field :actor_document_id, :string
    field :actor_type, Ecto.Enum, values: @actor_types
    field :status_before, Ecto.Enum, values: @ticket_status
    field :status_after, Ecto.Enum, values: @ticket_status

    belongs_to(:ticket, Ticket, foreign_key: :ticket_id)

    timestamps(updated_at: false, type: :utc_datetime)
  end
end
