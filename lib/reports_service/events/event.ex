defmodule ReportsService.Events.Event do
  @moduledoc false

  use Ecto.Schema

  import Ecto.Query

  alias Ecto.Association.NotLoaded
  alias ReportsService.Events.Event
  alias ReportsService.Events.EventPermission
  alias ReportsService.Events.Organizer
  alias ReportsService.Events.TicketCategory
  alias ReportsService.Events.Variant
  alias ReportsService.Repo

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  # styler:sort
  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          promoter_id: Ecto.UUID.t(),
          organizer: Organizer.t() | NotLoaded.t() | nil,
          admission_date: DateTime.t() | nil,
          deleted_at: DateTime.t() | nil,
          end_date: DateTime.t() | nil,
          is_draft: boolean() | nil,
          start_date: DateTime.t() | nil,
          title: String.t() | nil,
          permissions: [EventPermission.t()] | NotLoaded.t(),
          ticket_categories: [TicketCategory.t()] | NotLoaded.t(),
          variants: [Variant.t()] | NotLoaded.t(),
          inserted_at: DateTime.t() | nil,
          updated_at: DateTime.t() | nil
        }

  # styler:sort
  schema "events" do
    belongs_to :organizer, Organizer, foreign_key: :promoter_id
    field :admission_date, :utc_datetime
    field :deleted_at, :utc_datetime
    field :end_date, :utc_datetime
    field :is_draft, :boolean
    field :start_date, :utc_datetime
    field :title, :string
    has_many :permissions, EventPermission
    has_many :ticket_categories, TicketCategory
    has_many :variants, Variant
    timestamps()
  end

  def get(event_id), do: Repo.get(Event, event_id)

  def get(event_id, preloads), do: Repo.one(from(e in Event, where: e.id == ^event_id, preload: ^preloads))

  @spec get_all_by_organizer_id(organizer_id :: Ecto.UUID.t()) :: [Event.t()]
  def get_all_by_organizer_id(organizer_id) do
    query =
      from(e in Event,
        where: e.promoter_id == ^organizer_id
      )

    Repo.all(query)
  end

  @spec get_by_ids(event_ids :: [Ecto.UUID.t()], preloads: [atom()]) :: [Event.t()]
  def get_by_ids(event_ids, preloads \\ []) do
    query =
      from(e in Event,
        where: e.id in ^event_ids,
        preload: ^preloads
      )

    Repo.all(query)
  end
end
