defmodule ReportsService.Events.Variant do
  @moduledoc false

  use Ecto.Schema

  alias ReportsService.Events.Event
  alias ReportsService.Events.MultiLevelPricingModifier
  alias ReportsService.Events.MultiLevelPricingModifierVariant
  alias ReportsService.Events.SalesChannel
  alias ReportsService.Events.TicketCategory

  @distribution_type [
    :GUEST_LIST_INVITATION,
    :REGULAR,
    :SALES_CHANNEL
  ]

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  schema "variants" do
    belongs_to :event, Event
    belongs_to :ticket_category, TicketCategory
    field :deleted_at, :utc_datetime
    field :distribution_type, Ecto.Enum, values: @distribution_type, default: :REGULAR
    field :quota, :integer
    field :unit_price, :integer

    has_one :sales_channel, SalesChannel

    many_to_many :mlpm,
                 MultiLevelPricingModifier,
                 join_through: MultiLevelPricingModifierVariant,
                 join_keys: [variant_id: :id, mlpm_id: :id]

    timestamps()
  end
end
