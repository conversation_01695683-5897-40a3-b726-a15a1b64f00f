defmodule ReportsService.Events.ChannelConfig do
  @moduledoc false

  use Ecto.Schema

  alias ReportsService.Events.Event

  # styler:sort
  @type t :: %__MODULE__{
          amount_of_objects: integer(),
          channel_key: Ecto.UUID.t(),
          color: String.t(),
          deleted_at: DateTime.t(),
          description: String.t(),
          event_id: Ecto.UUID.t(),
          id: Ecto.UUID.t(),
          label: String.t(),
          inserted_at: DateTime.t(),
          token: String.t(),
          type: atom(),
          updated_at: DateTime.t(),
          value: float(),
          valid_until: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  @schema_prefix :events
  # styler:sort
  schema "channel_configs" do
    belongs_to :event, Event

    field :amount_of_objects, :integer
    field :channel_key, :binary_id
    field :color, :string
    field :deleted_at, :utc_datetime_usec

    field :description, :string
    field :label, :string
    field :token, :string
    field :type, Ecto.Enum, values: [:percentage, :fixed]
    field :valid_until, :utc_datetime_usec
    field :value, :float
    timestamps()
  end
end
