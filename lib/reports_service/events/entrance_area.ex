defmodule ReportsService.Events.EntranceArea do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Query

  alias ReportsService.Events.EntranceArea
  alias ReportsService.Events.EntranceAreaTicketCategory
  alias ReportsService.Events.Event
  alias ReportsService.Events.TicketCategory
  alias ReportsService.Events.Variant
  alias ReportsService.Events.VariantCounter
  alias ReportsService.Orders.Ticket
  alias ReportsService.Orders.TicketHistory
  alias ReportsService.Repo

  require Logger

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  schema "entrance_areas" do
    field :deleted_at, :utc_datetime
    field :description, :string
    field :event_id, Ecto.UUID
    field :name, :string
    field :ticket_category_contingent, :integer, virtual: true
    field :ticket_category_count, :integer, virtual: true
    field :user_document_id, :string
    has_many :entrance_area_ticket_categories, EntranceAreaTicketCategory

    timestamps(type: :utc_datetime)
  end

  # styler:sort
  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          name: String.t(),
          description: String.t() | nil,
          event_id: Ecto.UUID.t(),
          user_document_id: String.t(),
          ticket_category_count: integer() | nil,
          ticket_category_contingent: integer() | nil,
          deleted_at: DateTime.t() | nil,
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @spec get_entrance_area_statistic_by_aggregator(map()) :: {:ok, map()} | {:error, any()} | nil
  def get_entrance_area_statistic_by_aggregator(params) do
    aggregator = Map.get(params, :aggregator)

    case aggregator do
      :total -> list_entrance_areas(params)
      :"entry-flow" -> generate_entry_flow(params)
      :sum -> header_total(params)
      :"product-overview" -> get_summary(params)
      :"time-lapse" -> get_time_lapse(params)
      _ -> nil
    end
  end

  @spec get_statistics_dates(map()) :: map()
  def get_statistics_dates(%{event_id: event_id} = params) do
    event = Event.get(event_id)
    do_get_statistics_dates(event, params)
  end

  @spec validate_statistics_date(map()) :: :ok | {:error, String.t()}
  def validate_statistics_date(%{event_id: event_id} = params) do
    start_date = Map.get(params, :start_date)
    end_date = Map.get(params, :end_date)

    event = Event.get(event_id)

    case validate_date(event, start_date, end_date) do
      :ok -> :ok
      :error -> :error
    end
  end

  @spec get_summary(map()) :: {:ok, any()}
  def get_summary(%{event_id: event_id, distribution_types: distribution_types} = params) do
    %{start_date: start_date, end_date: end_date} = get_statistics_dates(params)
    uuids = get_entrance_area_ids(params)

    default_uuid = Ecto.UUID.dump!("00000000-0000-0000-0000-000000000000")

    ticket_history_sub =
      from(th in TicketHistory,
        left_join: ea in EntranceArea,
        on: th.location_id == ea.id and ea.deleted_at == nil,
        select: %{
          id: th.id,
          location_id:
            fragment(
              "CASE WHEN ? IS NULL OR ? IS NOT NULL THEN ? ELSE ? END",
              th.location_id,
              ea.deleted_at,
              ^default_uuid,
              th.location_id
            ),
          inserted_at: th.inserted_at,
          ticket_id: th.ticket_id,
          status_after: th.status_after
        }
      )

    result =
      from(th in subquery(ticket_history_sub))
      |> join(:left, [th], t in Ticket, as: :ticket, on: th.ticket_id == t.id)
      |> join(:left, [th, t], v in Variant, as: :variant, on: t.variant_id == v.id)
      |> join(:left, [th, t, v], tc in TicketCategory, on: tc.id == v.ticket_category_id)
      |> join(:left, [th, t, v, tc], vc in VariantCounter, on: vc.variant_id == v.id)
      |> where(
        [th, t, v, tc, vc],
        t.event_id == ^event_id and th.status_after == :USED and t.admission == true and th.inserted_at >= ^start_date and
          th.inserted_at <= ^end_date and th.location_id in ^uuids
      )
      |> maybe_filter_by_distribution_types(distribution_types)
      |> order_by(^order_by_params_for_summaries(params))
      |> group_by([th, t, v, tc, vc], [tc.name, vc.existing_items, vc.sold])
      |> select([th, t, v, tc, vc], %{
        product: tc.name,
        total_validations: count(th.id),
        total_tickets: vc.existing_items,
        total_attendees: vc.existing_items,
        ratio: fragment("?::float / NULLIF(?::float, 0)", count(th.id), vc.existing_items)
      })
      |> Repo.paginate(params)

    {:ok, result}
  end

  @spec header_total(map()) :: {:ok, any()}
  def header_total(%{event_id: event_id, distribution_types: distribution_types} = params) do
    allow_not_assigned = Map.get(params, :allow_not_assigned)
    uuids = Map.get(params, :entrance_area_ids, [])

    uuids =
      if allow_not_assigned do
        Enum.map(["00000000-0000-0000-0000-000000000000" | uuids], &Ecto.UUID.dump!/1)
      else
        Enum.map(uuids, &Ecto.UUID.dump!/1)
      end

    %{start_date: start_date, end_date: end_date} = get_statistics_dates(params)

    total_sold_subquery =
      from(t in Ticket, as: :ticket)
      |> where([t], t.event_id == ^event_id)
      |> where([t], t.status in [:ACTIVE, :USED, :UNUSED])
      |> maybe_filter_by_distribution_types(distribution_types)
      |> select([t], count(t.id))

    default_uuid = Ecto.UUID.dump!("00000000-0000-0000-0000-000000000000")

    ticket_history_sub =
      from(th in TicketHistory,
        left_join: ea in EntranceArea,
        on: th.location_id == ea.id and ea.deleted_at == nil,
        select: %{
          id: th.id,
          location_id:
            fragment(
              "CASE WHEN ? IS NULL OR ? IS NOT NULL THEN ? ELSE ? END",
              th.location_id,
              ea.deleted_at,
              ^default_uuid,
              th.location_id
            ),
          inserted_at: th.inserted_at,
          ticket_id: th.ticket_id,
          status_after: th.status_after
        }
      )

    result =
      from(th in subquery(ticket_history_sub))
      |> join(:left, [th], t in Ticket, as: :ticket, on: th.ticket_id == t.id)
      |> join(:inner, [th, t], v in Variant, as: :variant, on: t.variant_id == v.id)
      |> where([th, t], t.event_id == ^event_id and th.status_after == :USED and t.admission == true)
      |> where([th, t], th.inserted_at >= ^start_date and th.inserted_at <= ^end_date)
      |> where([th, t], th.location_id in ^uuids)
      |> maybe_filter_by_distribution_types(distribution_types)
      |> select([th, t], %{
        totalValidations: count(th.id),
        totalTickets: subquery(total_sold_subquery),
        # NULLIF is used to check if the divisor is 0, in which case it returns NULL instead of throwing an error
        # COALESCE is used to return 0 if the divisor is NULL
        avgValidationsPerHour:
          fragment(
            "COALESCE(?::float / NULLIF((EXTRACT(EPOCH FROM AGE(?::timestamp, ?::timestamp)) / 3600), 0), 0)",
            count(th.id),
            ^end_date,
            ^start_date
          ),
        avgValidationsPerMinute:
          fragment(
            "COALESCE(?::float / NULLIF((EXTRACT(EPOCH FROM AGE(?::timestamp, ?::timestamp)) / 60), 0), 0)",
            count(th.id),
            ^end_date,
            ^start_date
          )
      })
      |> Repo.one()

    {:ok, result}
  end

  @spec list_entrance_areas(map()) :: {:ok, any()}
  def list_entrance_areas(%{event_id: event_id} = params) do
    %{start_date: start_date, end_date: end_date} = get_statistics_dates(params)
    {offset, page_size} = get_offset_limit(params)
    order_by = get_order_by_params(params)
    order_dir = params |> Map.get(:order, :desc) |> Atom.to_string()
    allow_not_assigned = Map.get(params, :allow_not_assigned)
    uuids = Map.get(params, :entrance_area_ids, [])

    uuids =
      if allow_not_assigned do
        Enum.map(["00000000-0000-0000-0000-000000000000" | uuids], &Ecto.UUID.dump!/1)
      else
        Enum.map(uuids, &Ecto.UUID.dump!/1)
      end

    total_entries_query = get_entrance_validation_metrics_query(params)
    query = get_paginated_entrance_validation_metrics_query(params, order_by, order_dir)

    with {_, {:ok, %Postgrex.Result{rows: total_entries_rows, columns: _columns}}} <-
           {:total_entries, Repo.query(total_entries_query, [end_date, start_date, event_id, uuids])},
         {_, {:ok, %Postgrex.Result{rows: rows, columns: _columns}}} <-
           {:entries, Repo.query(query, [end_date, start_date, event_id, uuids, page_size, offset])} do
      columns = [
        :entrance_area_id,
        :entrance_area_name,
        :total_validations,
        :avg_validations_per_minute,
        :avg_validations_per_hour
      ]

      result = Enum.map(rows, fn row -> columns |> Enum.zip(row) |> Map.new() end)

      {:ok,
       %Scrivener.Page{
         entries: result,
         page_number: get_page(params),
         page_size: page_size,
         total_entries: length(total_entries_rows),
         total_pages: ceil(length(total_entries_rows) / page_size)
       }}
    else
      {_, {:error, reason}} ->
        Logger.error("Error fetching entrance areas statistic for event #{event_id}: #{inspect(reason)}")
        {:error, reason}
    end
  end

  def get_time_lapse(%{event_id: event_id} = params) do
    query = get_time_lapse_query(params)

    %{start_date: start_date, end_date: end_date} = get_statistics_dates(params)
    {rounded_start_date, rounded_end_date} = round_to_30_minute_intervals(start_date, end_date)
    allow_not_assigned = Map.get(params, :allow_not_assigned)

    uuids = Map.get(params, :entrance_area_ids, [])

    uuids =
      if allow_not_assigned do
        Enum.map(["00000000-0000-0000-0000-000000000000" | uuids], &Ecto.UUID.dump!/1)
      else
        Enum.map(uuids, &Ecto.UUID.dump!/1)
      end

    case Repo.query(query, [rounded_start_date, rounded_end_date, event_id, uuids]) do
      {:ok, %Postgrex.Result{rows: rows, columns: _columns}} ->
        columns = [:startTime, :count]

        result =
          Enum.map(rows, fn row ->
            item = columns |> Enum.zip(row) |> Map.new()
            %{item | count: Decimal.to_integer(item.count)}
          end)

        # Fill in the missing intervals with 0 counts if there are previous intervals with counts > 0
        # this value is used as the starting point to avoid voids in the graph
        result = merge_data(result, generate_30_minute_intervals(rounded_start_date, rounded_end_date))
        {:ok, result}

      {:error, reason} ->
        Logger.error("Error fetching entrance areas statistic for event #{event_id}: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @spec generate_entry_flow(map()) :: {:ok, any()} | {:error, any()}
  def generate_entry_flow(params) do
    case query_entry_flow(params) do
      {:ok, raw_data} ->
        result = Enum.map(raw_data, fn row -> %{row | entranceAreaId: Ecto.UUID.load!(row.entranceAreaId)} end)
        {:ok, result}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @spec maybe_set_allow_not_assigned_default_value(map()) :: map()
  def maybe_set_allow_not_assigned_default_value(%{allow_not_assigned: _} = params), do: params

  def maybe_set_allow_not_assigned_default_value(params), do: Map.put(params, :allow_not_assigned, true)

  @spec query_entry_flow(map()) :: {:ok, any()} | {:error, any()}
  def query_entry_flow(%{event_id: event_id} = params) do
    query = get_entry_flow_query(params)

    %{start_date: start_date, end_date: end_date} = get_statistics_dates(params)
    {rounded_start_date, rounded_end_date} = round_to_30_minute_intervals(start_date, end_date)
    allow_not_assigned = Map.get(params, :allow_not_assigned)

    uuids = Map.get(params, :entrance_area_ids, [])

    uuids =
      if allow_not_assigned do
        Enum.map(["00000000-0000-0000-0000-000000000000" | uuids], &Ecto.UUID.dump!/1)
      else
        Enum.map(uuids, &Ecto.UUID.dump!/1)
      end

    case Repo.query(query, [rounded_start_date, rounded_end_date, event_id, uuids]) do
      {:ok, %Postgrex.Result{rows: rows, columns: _columns}} ->
        columns = [:startTime, :entranceAreaId, :name, :count]
        result = Enum.map(rows, fn row -> columns |> Enum.zip(row) |> Map.new() end)
        {:ok, result}

      {:error, reason} ->
        Logger.error("Error fetching entrance areas statistic for event #{event_id}: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp get_event_start(%Event{start_date: start_date}) when not is_nil(start_date), do: start_date
  defp get_event_start(_event), do: DateTime.utc_now()

  defp get_statistics_start(%Event{admission_date: admission_date}) when not is_nil(admission_date), do: admission_date

  defp get_statistics_start(%Event{start_date: start_date}) when not is_nil(start_date), do: start_date
  defp get_statistics_start(_event), do: DateTime.utc_now()

  defp get_statistics_end(%Event{end_date: nil} = event), do: DateTime.add(get_event_start(event), 12, :hour)

  defp get_statistics_end(%Event{end_date: end_date}), do: end_date

  defp validate_date(_, nil, nil), do: :ok

  defp validate_date(event, start_date, nil) do
    stats_start_date = get_statistics_start(event)
    lower_bound = Timex.shift(stats_start_date, hours: -24)

    if Timex.before?(start_date, lower_bound) do
      :error
    else
      :ok
    end
  end

  defp validate_date(event, nil, end_date) do
    stats_end_date = get_statistics_end(event)
    upper_bound = Timex.shift(stats_end_date, hours: 24)

    if Timex.after?(end_date, upper_bound) do
      :error
    else
      :ok
    end
  end

  defp validate_date(event, start_date, end_date) do
    stats_start_date = get_statistics_start(event)
    stats_end_date = get_statistics_end(event)
    lower_bound = Timex.shift(stats_start_date, hours: -24)
    upper_bound = Timex.shift(stats_end_date, hours: 24)

    cond do
      Timex.before?(start_date, lower_bound) ->
        :error

      Timex.after?(end_date, upper_bound) ->
        :error

      true ->
        :ok
    end
  end

  defp do_get_statistics_dates(_event, %{start_date: start_date, end_date: end_date}) do
    %{start_date: start_date, end_date: end_date}
  end

  defp do_get_statistics_dates(event, %{start_date: start_date}) do
    %{start_date: start_date, end_date: get_statistics_end(event)}
  end

  defp do_get_statistics_dates(event, %{end_date: end_date}) do
    %{start_date: get_statistics_start(event), end_date: end_date}
  end

  defp do_get_statistics_dates(event, _params) do
    %{start_date: get_statistics_start(event), end_date: get_statistics_end(event)}
  end

  defp order_by_params_for_summaries(%{order: order_dir} = params) when order_dir in [:desc, :asc],
    do: order_by_params_for_summary(params, order_dir)

  defp order_by_params_for_summaries(params), do: order_by_params_for_summary(params, :desc)
  defp order_by_params_for_summary(%{sort: :name}, order_dir), do: [{order_dir, dynamic([th, t, v, tc, vc, sp], tc.name)}]

  defp order_by_params_for_summary(%{sort: :"total-tickets"}, order_dir),
    do: [{order_dir, dynamic([th, t, v, tc, vc], vc.existing_items)}]

  defp order_by_params_for_summary(%{sort: :"total-attendees"}, order_dir),
    do: [{order_dir, dynamic([th, t, v, tc, vc], vc.existing_items)}]

  defp order_by_params_for_summary(%{sort: :"total-validations"}, order_dir),
    do: [{order_dir, dynamic([th, t, v, tc, vc], count(th.id))}]

  defp order_by_params_for_summary(%{sort: :ratio}, order_dir),
    do: [{order_dir, dynamic([th, t, v, tc, vc], fragment("?::float / NULLIF(?::float, 0)", count(th.id), vc.sold))}]

  defp order_by_params_for_summary(_params, order_dir), do: [{order_dir, dynamic([th, t, v, tc, vc], tc.name)}]

  defp round_to_30_minute_intervals(start_date, end_date) do
    rounded_start =
      Timex.set(start_date, minute: div(start_date.minute, 30) * 30, second: 0)

    rem_end_date = rem(end_date.minute, 30)
    minutes = if rem_end_date == 0, do: 0, else: 30 - rem_end_date

    rounded_end =
      end_date
      |> Timex.shift(minutes: minutes)
      |> Timex.set(second: 0)

    {rounded_start, rounded_end}
  end

  defp get_offset_limit(params) do
    page_limit = get_page_size(params)
    page = get_page(params)
    offset = (page - 1) * page_limit
    {offset, page_limit}
  end

  defp generate_30_minute_intervals(start_date, end_date) do
    start_date
    |> Stream.iterate(&Timex.shift(&1, minutes: 30))
    |> Enum.take_while(&(Timex.compare(&1, end_date) != 1))
  end

  defp merge_data([], _), do: []

  defp merge_data(counts_list, timestamps_list) do
    # Convert counts_list to a map for efficient lookup
    counts_map =
      Enum.reduce(counts_list, %{}, fn %{count: count, startTime: startTime}, acc ->
        Map.put(acc, NaiveDateTime.to_iso8601(startTime), count)
      end)

    # Extend timestamps_list if needed, based on the maximum timestamp in counts_list
    max_timestamp =
      counts_list
      |> Enum.map(& &1.startTime)
      |> Enum.max_by(&NaiveDateTime.to_erl/1)
      |> DateTime.from_naive!("Etc/UTC")

    extended_timestamps_list =
      timestamps_list ++ generate_series(List.last(timestamps_list), max_timestamp)

    # Iterate over timestamps_list and retrieve counts, using the previous count if not present
    extended_timestamps_list
    |> Enum.reduce({[], 0}, fn timestamp, {acc, last_count} ->
      naive_timestamp = timestamp |> DateTime.to_naive() |> NaiveDateTime.to_iso8601()
      count = Map.get(counts_map, naive_timestamp, last_count)
      {[%{startTime: timestamp, count: count} | acc], count}
    end)
    |> elem(0)
    |> Enum.reverse()
  end

  defp generate_series(nil, _), do: []
  defp generate_series(start_time, start_time), do: []

  defp generate_series(start_time, end_time) do
    start_time
    |> Stream.iterate(&Timex.shift(&1, minutes: 30))
    |> Enum.take_while(&(Timex.compare(&1, end_time) != 1))
  end

  defp get_page(%{page: page}) when page > 0, do: page
  defp get_page(_), do: 1
  defp get_page_size(%{page_size: page_size}) when page_size > 0, do: page_size
  defp get_page_size(_), do: 10

  defp get_order_by_params(%{sort: :hour}), do: "avg_validations_per_hour"
  defp get_order_by_params(%{sort: :minute}), do: "avg_validations_per_minute"
  defp get_order_by_params(%{sort: :name}), do: "entrance_name"
  defp get_order_by_params(%{sort: :total}), do: "total_validated"
  defp get_order_by_params(_), do: "entrance_name"

  defp get_entrance_area_ids(params) do
    allow_not_assigned = Map.get(params, :allow_not_assigned)
    uuids = Map.get(params, :entrance_area_ids, [])

    if allow_not_assigned do
      Enum.map(["00000000-0000-0000-0000-000000000000" | uuids], &Ecto.UUID.dump!/1)
    else
      Enum.map(uuids, &Ecto.UUID.dump!/1)
    end
  end

  defp maybe_filter_by_distribution_types(query, nil), do: query
  defp maybe_filter_by_distribution_types(query, []), do: query

  defp maybe_filter_by_distribution_types(query, distribution_types),
    do: where(query, [ticket: t], t.distribution_type in ^distribution_types)

  defp get_entrance_validation_metrics_query(%{distribution_types: nil} = _params) do
    """
      select
      location_id::text, entrance_name,
      count(ticket_history_id) as total_validated,
      COUNT(ticket_history_id)::float / (EXTRACT(EPOCH FROM AGE($1::timestamp, $2::timestamp)) / 60) AS avg_validations_per_minute,
      COUNT(ticket_history_id)::float / (EXTRACT(EPOCH FROM AGE($1::timestamp, $2::timestamp)) / 3600 ) AS avg_validations_per_hour
      from
      (select  th.id as ticket_history_id,
         CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN '00000000-0000-0000-0000-000000000000' ELSE th.location_id END AS location_id,
         CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN 'NOT ASSIGNED' ELSE COALESCE(ea.name, 'NOT ASSIGNED') END AS entrance_name
      from orders.tickets t
      left join orders.ticket_histories th on th.ticket_id = t.id and th.status_after = 'USED'
      left join events.entrance_areas ea on th.location_id = ea.id
      where t.event_id::text = $3  and t.admission = true and  th."inserted_at" between $2::timestamp AND $1::timestamp) subquery
      where location_id = ANY($4::uuid[]
      )
      group by entrance_name, location_id
    """
  end

  defp get_entrance_validation_metrics_query(%{distribution_types: distribution_types} = _params) do
    """
      select
      location_id::text, entrance_name,
      count(ticket_history_id) as total_validated,
      COUNT(ticket_history_id)::float / (EXTRACT(EPOCH FROM AGE($1::timestamp, $2::timestamp)) / 60) AS avg_validations_per_minute,
      COUNT(ticket_history_id)::float / (EXTRACT(EPOCH FROM AGE($1::timestamp, $2::timestamp)) / 3600 ) AS avg_validations_per_hour
      from
      (select  th.id as ticket_history_id,
         CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN '00000000-0000-0000-0000-000000000000' ELSE th.location_id END AS location_id,
         CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN 'NOT ASSIGNED' ELSE COALESCE(ea.name, 'NOT ASSIGNED') END AS entrance_name
      from orders.tickets t
      left join orders.ticket_histories th on th.ticket_id = t.id and th.status_after = 'USED'
      left join events.entrance_areas ea on th.location_id = ea.id
      inner join events.variants v on t.variant_id = v.id
      where t.event_id::text = $3  and t.admission = true and t.distribution_type in ('#{Enum.join(distribution_types, "','")}')
        and th."inserted_at" between $2::timestamp AND $1::timestamp) subquery
      where location_id = ANY($4::uuid[]
      )
      group by entrance_name, location_id
    """
  end

  defp get_paginated_entrance_validation_metrics_query(%{distribution_types: nil} = _params, order_by, order_dir) do
    """
      select
     location_id::text, entrance_name,
     count(ticket_history_id) as total_validated,
     COUNT(ticket_history_id)::float / (EXTRACT(EPOCH FROM AGE($1::timestamp, $2::timestamp)) / 60) AS avg_validations_per_minute,
     COUNT(ticket_history_id)::float / (EXTRACT(EPOCH FROM AGE($1::timestamp, $2::timestamp)) / 3600 ) AS avg_validations_per_hour
    from
    (select  th.id as ticket_history_id,
        CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN '00000000-0000-0000-0000-000000000000' ELSE th.location_id END AS location_id,
        CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN 'NOT ASSIGNED' ELSE COALESCE(ea.name, 'NOT ASSIGNED') END AS entrance_name
     from orders.tickets t
     left join orders.ticket_histories th on th.ticket_id = t.id and th.status_after = 'USED'
     left join events.entrance_areas ea on th.location_id = ea.id
     where t.event_id::text = $3  and t.admission = true and  th."inserted_at" between $2::timestamp AND $1::timestamp) subquery
     where location_id = ANY($4::uuid[]
    )
    group by entrance_name, location_id
    order by #{order_by} #{order_dir}
    limit $5 offset $6;
    """
  end

  defp get_paginated_entrance_validation_metrics_query(
         %{distribution_types: distribution_types} = _params,
         order_by,
         order_dir
       ) do
    """
      select
     location_id::text, entrance_name,
     count(ticket_history_id) as total_validated,
     COUNT(ticket_history_id)::float / (EXTRACT(EPOCH FROM AGE($1::timestamp, $2::timestamp)) / 60) AS avg_validations_per_minute,
     COUNT(ticket_history_id)::float / (EXTRACT(EPOCH FROM AGE($1::timestamp, $2::timestamp)) / 3600 ) AS avg_validations_per_hour
    from
    (select  th.id as ticket_history_id,
        CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN '00000000-0000-0000-0000-000000000000' ELSE th.location_id END AS location_id,
        CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN 'NOT ASSIGNED' ELSE COALESCE(ea.name, 'NOT ASSIGNED') END AS entrance_name
     from orders.tickets t
     left join orders.ticket_histories th on th.ticket_id = t.id and th.status_after = 'USED'
     left join events.entrance_areas ea on th.location_id = ea.id
     inner join events.variants v on t.variant_id = v.id
     where t.event_id::text = $3 and t.admission = true and t.distribution_type in ('#{Enum.join(distribution_types, "','")}')
       and th."inserted_at" between $2::timestamp AND $1::timestamp) subquery
     where location_id = ANY($4::uuid[]
    )
    group by entrance_name, location_id
    order by #{order_by} #{order_dir}
    limit $5 offset $6;
    """
  end

  defp get_entry_flow_query(%{distribution_types: nil} = _params) do
    """
    select to_char (interval_start at time zone 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS"Z"'), location_id, entrance_name, count(validated_time) from(
      with time_slots as(
        SELECT generate_series( $1::timestamp, $2::timestamp, '30 minutes'::interval ) AS interval_start
      )
      select * from time_slots ts
      cross join (select th.inserted_at as validated_time,
        CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN '00000000-0000-0000-0000-000000000000' ELSE th.location_id END AS location_id,
        CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN 'NOT ASSIGNED' ELSE COALESCE(ea.name, 'NOT ASSIGNED') END AS entrance_name
        from orders.tickets t
        left join orders.ticket_histories th on th.ticket_id = t.id and th.status_after = 'USED'
        left join events.entrance_areas ea on th.location_id = ea.id
           where t.event_id::text = $3 and t.admission = true
     ) new_ticket_history
      where
      date_trunc('hour', new_ticket_history.validated_time) +
      (date_part('minute', new_ticket_history.validated_time)::integer / 30) * interval '30 min' = ts.interval_start
      order by ts.interval_start)
    as subquery
    where location_id  =  ANY($4::uuid[])
    group by location_id, interval_start, entrance_name
    """
  end

  defp get_entry_flow_query(%{distribution_types: distribution_types} = _params) do
    """
    select to_char (interval_start at time zone 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS"Z"'), location_id, entrance_name, count(validated_time) from(
      with time_slots as(
        SELECT generate_series( $1::timestamp, $2::timestamp, '30 minutes'::interval ) AS interval_start
      )
      select * from time_slots ts
      cross join (select th.inserted_at as validated_time,
        CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN '00000000-0000-0000-0000-000000000000' ELSE th.location_id END AS location_id,
        CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN 'NOT ASSIGNED' ELSE COALESCE(ea.name, 'NOT ASSIGNED') END AS entrance_name
        from orders.tickets t
        left join orders.ticket_histories th on th.ticket_id = t.id and th.status_after = 'USED'
        left join events.entrance_areas ea on th.location_id = ea.id
        inner join events.variants v on t.variant_id = v.id
        where t.event_id::text = $3 and t.admission = true and t.distribution_type in ('#{Enum.join(distribution_types, "','")}')
     ) new_ticket_history
      where
      date_trunc('hour', new_ticket_history.validated_time) +
      (date_part('minute', new_ticket_history.validated_time)::integer / 30) * interval '30 min' = ts.interval_start
      order by ts.interval_start)
    as subquery
    where location_id  =  ANY($4::uuid[])
    group by location_id, interval_start, entrance_name
    """
  end

  defp get_time_lapse_query(%{distribution_types: nil} = _params) do
    """
        WITH initial_validations AS (
            -- Calculate the total validations before the given start time ($1)
            SELECT COALESCE(COUNT(th.inserted_at), 0) AS pre_start_validations
            FROM orders.tickets t
            LEFT JOIN orders.ticket_histories th ON th.ticket_id = t.id AND th.status_after = 'USED'
            LEFT JOIN events.entrance_areas ea ON th.location_id = ea.id
            WHERE t.event_id::text = $3
              AND t.admission = true
              AND th.inserted_at <= $1::timestamp
              AND (CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN '00000000-0000-0000-0000-000000000000' ELSE th.location_id END) = ANY($4::uuid[])
        ),
        interval_validations AS (
            -- Generate the time slots and count validations within each 30-minute interval
            SELECT interval_start, validation_count, location_id FROM (
            SELECT
                ts.interval_start,
                COUNT(th.inserted_at) AS validation_count,
                CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN '00000000-0000-0000-0000-000000000000' ELSE th.location_id END AS location_id
            FROM (
                SELECT generate_series($1::timestamp, $2::timestamp, '30 minutes'::interval) AS interval_start
            ) ts
            LEFT JOIN orders.ticket_histories th ON
                date_trunc('hour', th.inserted_at) +
                (date_part('minute', th.inserted_at)::integer / 30) * interval '30 min' = ts.interval_start
            LEFT JOIN orders.tickets t ON th.ticket_id = t.id AND t.admission = true
            LEFT JOIN events.entrance_areas ea ON th.location_id = ea.id
            WHERE t.event_id::text = $3 AND th.status_after = 'USED'
            GROUP BY ts.interval_start, th.location_id, ea.deleted_at ) as subquery
            WHERE subquery.location_id = ANY($4::uuid[])
        )
        SELECT
            DISTINCT(interval_start),
            SUM(validation_count) OVER (ORDER BY interval_start)
            + (SELECT pre_start_validations FROM initial_validations) AS cumulative_validations
        FROM interval_validations
        ORDER BY interval_start;
    """
  end

  defp get_time_lapse_query(%{distribution_types: distribution_types} = _params) do
    """
        WITH initial_validations AS (
            -- Calculate the total validations before the given start time ($1)
            SELECT COALESCE(COUNT(th.inserted_at), 0) AS pre_start_validations
            FROM orders.tickets t
            LEFT JOIN orders.ticket_histories th ON th.ticket_id = t.id AND th.status_after = 'USED'
            LEFT JOIN events.entrance_areas ea ON th.location_id = ea.id
            INNER JOIN events.variants v on t.variant_id = v.id
            WHERE t.event_id::text = $3
              AND t.admission = true
              AND th.inserted_at <= $1::timestamp
              AND t.distribution_type in ('#{Enum.join(distribution_types, "','")}')
              AND (CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN '00000000-0000-0000-0000-000000000000' ELSE th.location_id END) = ANY($4::uuid[])
        ),
        interval_validations AS (
            -- Generate the time slots and count validations within each 30-minute interval
            SELECT interval_start, validation_count, location_id FROM (
            SELECT
                ts.interval_start,
                COUNT(th.inserted_at) AS validation_count,
                CASE WHEN th.location_id IS NULL OR ea.deleted_at IS NOT NULL THEN '00000000-0000-0000-0000-000000000000' ELSE th.location_id END AS location_id
            FROM (
                SELECT generate_series($1::timestamp, $2::timestamp, '30 minutes'::interval) AS interval_start
            ) ts
            LEFT JOIN orders.ticket_histories th ON
                date_trunc('hour', th.inserted_at) +
                (date_part('minute', th.inserted_at)::integer / 30) * interval '30 min' = ts.interval_start
            LEFT JOIN orders.tickets t ON th.ticket_id = t.id AND t.admission = true
            LEFT JOIN events.entrance_areas ea ON th.location_id = ea.id
            INNER JOIN events.variants v on t.variant_id = v.id
            WHERE t.event_id::text = $3
              AND th.status_after = 'USED'
              AND t.distribution_type in ('#{Enum.join(distribution_types, "','")}')
            GROUP BY ts.interval_start, th.location_id, ea.deleted_at ) as subquery
            WHERE subquery.location_id = ANY($4::uuid[])
        )
        SELECT
            DISTINCT(interval_start),
            SUM(validation_count) OVER (ORDER BY interval_start)
            + (SELECT pre_start_validations FROM initial_validations) AS cumulative_validations
        FROM interval_validations
        ORDER BY interval_start;
    """
  end
end
