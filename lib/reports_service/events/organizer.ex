defmodule ReportsService.Events.Organizer do
  @moduledoc false

  use Ecto.Schema

  import Ecto.Query

  alias ReportsService.Events.Organizer
  alias ReportsService.Repo

  # styler:sort
  @type t :: %__MODULE__{
          created_by: Ecto.UUID.t(),
          created_by_document_id: String.t(),
          deleted_at: DateTime.t() | nil,
          firestore_id: String.t(),
          id: Ecto.UUID.t(),
          inserted_at: DateTime.t(),
          updated_at: DateTime.t()
        }

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  schema "promoters" do
    field :created_by, Ecto.UUID
    field :created_by_document_id, :string
    field :deleted_at, :utc_datetime
    field :firestore_id, :string

    timestamps(type: :utc_datetime)
  end

  def get_organizer_by_creator(user_id) do
    case Ecto.UUID.cast(user_id) do
      {:ok, _user_uuid} ->
        Repo.one(
          from(o in Organizer,
            where: o.created_by == ^user_id
          )
        )

      _error ->
        Repo.one(
          from(o in Organizer,
            where: o.created_by_document_id == ^user_id
          )
        )
    end
  end
end
