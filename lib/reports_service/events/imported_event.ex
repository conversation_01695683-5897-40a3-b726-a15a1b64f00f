defmodule ReportsService.Events.ImportedEvent do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query

  alias Ecto.Multi
  alias ReportsService.Events.ImportedEvent
  alias ReportsService.Orders.ImportedOrder
  alias ReportsService.Repo

  require Logger

  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          title: String.t(),
          organizer_id: Ecto.UUID.t(),
          deleted_at: DateTime.t()
        }

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "imported_events" do
    field :title, :string
    field :organizer_id, Ecto.UUID
    field :start_date, :utc_datetime
    field :deleted_at, :utc_datetime

    timestamps(type: :utc_datetime)
  end

  def changeset(imported_events, attrs) do
    imported_events
    |> cast(attrs, [:organizer_id, :title, :start_date, :deleted_at])
    |> validate_required([:organizer_id, :title, :start_date])
  end

  @spec get(imported_event_id :: Ecto.UUID.t()) :: ImportedEvent.t() | nil
  def get(imported_event_id), do: Repo.get(ImportedEvent, imported_event_id)

  @spec get_all_by_organizer_id(organizer_id :: Ecto.UUID.t()) :: [ImportedEvent.t()]
  def get_all_by_organizer_id(organizer_id) do
    query =
      from(ie in ImportedEvent,
        where: ie.organizer_id == ^organizer_id
      )

    Repo.all(query)
  end

  @spec create(event :: ImportedEvent.t(), orders :: [ImportedOrder.t()]) :: {:ok, ImportedEvent.t()} | {:error, any()}
  def create(event, orders) do
    multi = Multi.insert(Multi.new(), :event, changeset(%ImportedEvent{}, event))

    multi =
      Enum.reduce(orders, multi, fn order, multi ->
        Multi.insert(multi, "order_#{order.external_order_id}", fn %{event: %{id: event_id}} ->
          order = Map.put(order, :imported_event_id, event_id)
          ImportedOrder.changeset(%ImportedOrder{}, order)
        end)
      end)

    case Repo.transaction(multi, timeout: 60_000) do
      {:ok, response} ->
        {:ok, response.event}

      {:error, response} ->
        Logger.error("Can't create imported event becasue of #{inspect(response)}")
        {:error, response}

      error ->
        Logger.error("Can't create imported event becasue of #{inspect(error)}")
        {:error, error}
    end
  end
end
