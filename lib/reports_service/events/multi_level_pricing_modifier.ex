defmodule ReportsService.Events.MultiLevelPricingModifier do
  @moduledoc false

  use Ecto.Schema

  alias ReportsService.Events.MultiLevelPricing
  alias ReportsService.Events.MultiLevelPricingModifierVariant
  alias ReportsService.Events.Variant

  # styler:sort
  @type t ::
          %__MODULE__{
            deleted_at: DateTime.t() | nil,
            description: String.t() | nil,
            flat_modifier: Money.t() | nil,
            id: binary(),
            inserted_at: DateTime.t(),
            label: String.t(),
            max_amount: integer() | nil,
            min_amount: integer() | nil,
            mlp: MultiLevelPricing.t() | nil,
            mlp_id: binary(),
            percentage_modifier: float() | nil,
            updated_at: DateTime.t(),
            variants: [Variant.t()] | nil
          }

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  schema "mlpm" do
    belongs_to :mlp, MultiLevelPricing

    field :deleted_at, :utc_datetime
    field :description, :string
    field :flat_modifier, Money.Ecto.Composite.Type
    field :label, :string
    field :max_amount, :integer
    field :min_amount, :integer
    field :percentage_modifier, :float

    many_to_many :variants, Variant, join_through: MultiLevelPricingModifierVariant

    timestamps()
  end
end
