defmodule ReportsService.Events.EntranceAreaTicketCategory do
  @moduledoc false
  use Ecto.Schema

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  schema "entrance_area_ticket_categories" do
    field :entrance_area_id, Ecto.UUID
    field :ticket_category_id, Ecto.UUID
    timestamps(type: :utc_datetime, updated_at: false)
  end

  # styler:sort
  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          ticket_category_id: Ecto.UUID.t(),
          entrance_area_id: Ecto.UUID.t(),
          inserted_at: DateTime.t()
        }
end
