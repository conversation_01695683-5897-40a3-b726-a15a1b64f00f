defmodule ReportsService.Events.Voucher do
  @moduledoc false

  use Ecto.Schema

  import Ecto.Query

  alias ReportsService.Events.Voucher
  alias ReportsService.Repo

  @scope_type [
    :ORGANIZER,
    :EVENT,
    :TICKET_CATEGORY,
    :VARIANT
  ]

  # styler:sort
  @type t ::
          %__MODULE__{
            code: String.t(),
            deleted_at: NaiveDateTime.t() | nil,
            description: String.t(),
            scope_id: Ecto.UUID.t(),
            id: Ecto.UUID.t(),
            inserted_at: NaiveDateTime.t(),
            updated_at: NaiveDateTime.t(),
            value: integer()
          }

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  schema "vouchers" do
    field :code, :string
    field :deleted_at, :utc_datetime

    field :description, :string
    field :scope, Ecto.Enum, values: @scope_type
    field :scope_id, :binary_id
    field :value, :integer

    timestamps()
  end

  @spec get_by_ids(voucher_ids :: [Ecto.UUID.t()]) :: Voucher.t()
  def get_by_ids([]), do: []

  def get_by_ids(voucher_ids) do
    query =
      from(v in Voucher,
        where: v.id in ^voucher_ids and is_nil(v.deleted_at)
      )

    Repo.all(query)
  end
end
