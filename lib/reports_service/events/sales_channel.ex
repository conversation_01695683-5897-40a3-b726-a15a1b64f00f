defmodule ReportsService.Events.SalesChannel do
  @moduledoc false

  use Ecto.Schema

  alias ReportsService.Events.ChannelConfig
  alias ReportsService.Events.Variant

  @quota_modes [:RESERVED, :SHARED]

  # styler:sort
  @type t ::
          %__MODULE__{
            channel_config: ChannelConfig.t() | nil,
            channel_config_id: Ecto.UUID.t(),
            deleted_at: DateTime.t() | nil,
            id: Ecto.UUID.t(),
            inserted_at: DateTime.t(),
            original_price: integer(),
            updated_at: DateTime.t(),
            variant: Variant.t() | Ecto.Association.NotLoaded.t(),
            variant_id: Ecto.UUID.t(),
            quota_mode: atom()
          }

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  schema "sales_channels" do
    belongs_to :channel_config, ChannelConfig
    belongs_to :variant, Variant

    field :deleted_at, :utc_datetime
    field :original_price, :integer
    field :quota_mode, Ecto.Enum, values: @quota_modes, default: :RESERVED

    timestamps()
  end
end
