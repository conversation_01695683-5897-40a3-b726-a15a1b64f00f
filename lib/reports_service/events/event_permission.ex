defmodule ReportsService.Events.EventPermission do
  @moduledoc false

  use Ecto.Schema

  import Ecto.Query

  alias ReportsService.Events.Event
  alias ReportsService.Events.EventPermission
  alias ReportsService.Repo
  alias ReportsService.SellerPermissions

  require Logger

  # styler:sort
  @type t :: %__MODULE__{
          id: Ecto.UUID.t(),
          event_id: Ecto.UUID.t(),
          event: Event.t() | Ecto.Association.NotLoaded.t() | nil,
          deleted_at: DateTime.t() | nil,
          inherited_from_seller?: boolean() | nil,
          role: [atom()] | nil,
          user_document_id: String.t() | nil,
          user_id: Ecto.UUID.t() | nil,
          inserted_at: DateTime.t() | nil,
          updated_at: DateTime.t() | nil
        }

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # Rename :PROMOTER to :ORGANIZER can only be made in events-service
  # styler:sort
  schema "event_permissions" do
    belongs_to :event, Event

    field :deleted_at, :utc_datetime
    field :inherited_from_seller?, :boolean, default: false, source: :is_inherited_from_seller
    field :role, {:array, Ecto.Enum}, values: [:ADMIN, :PROMOTER, :EVENT_ADMIN, :SECURITY]
    field :user_document_id, :string
    field :user_id, Ecto.UUID

    timestamps()
  end

  def has_event_permission(user_id, event_id, permission, roles) do
    if Enum.member?(roles, "ADMIN"), do: true, else: has_event_permission(user_id, event_id, permission)
  end

  def has_event_permission(:ADMIN, _event_id, _permission), do: true

  def has_event_permission(user_id, event_id, permission) do
    # TODO: SD1-2087
    # case (and _error - path) can be removed after accounts-service deployment
    case Ecto.UUID.dump(user_id) do
      {:ok, _user_uuid} ->
        query =
          from(ep in EventPermission,
            where: ep.event_id == ^event_id,
            where: ep.user_id == ^user_id,
            where:
              ^:ADMIN in ep.role or ^:PROMOTER in ep.role or
                ^permission in ep.role,
            select: count(ep.id)
          )

        if Repo.one(query) > 0, do: true, else: false

      _error ->
        query =
          from(ep in EventPermission,
            where: ep.event_id == ^event_id,
            where: ep.user_document_id == ^user_id,
            where:
              ^:ADMIN in ep.role or ^:PROMOTER in ep.role or
                ^permission in ep.role,
            select: count(ep.id)
          )

        if Repo.one(query) > 0, do: true, else: false
    end
  end

  @doc """
  Checks if a user has access to an event, considering both direct event permissions
  and seller context permissions.

  ## Parameters
    * `event_id` - The event ID to check access for
    * `role` - The required permission (e.g., :EVENT_ADMIN)
    * `user_id` - The user ID
    * `seller_id` - Optional seller ID for seller context (default: nil)
    * `roles` - Optional user roles list (default: [])

  ## Returns
    * `true` if user has access
    * `false` if user does not have access
  """
  @spec has_access?(
          event_id :: Ecto.UUID.t(),
          role :: atom(),
          user_id :: String.t()
        ) :: boolean()
  @spec has_access?(
          event_id :: Ecto.UUID.t(),
          role :: atom(),
          user_id :: String.t(),
          seller_id :: String.t() | nil
        ) :: boolean()
  @spec has_access?(
          event_id :: Ecto.UUID.t(),
          role :: atom(),
          user_id :: String.t(),
          seller_id :: String.t() | nil,
          roles :: [String.t()]
        ) :: boolean()
  def has_access?(event_id, role, user_id, seller_id \\ nil, roles \\ [])

  def has_access?(event_id, role, user_id, seller_id, roles) do
    Logger.debug("Check if user #{inspect(user_id)} has access to event #{inspect(event_id)} with role #{inspect(role)}")

    cond do
      admin_user?(roles) ->
        true

      not is_nil(seller_id) ->
        seller_and_user_access?(event_id, role, user_id, seller_id)

      true ->
        has_event_permission(user_id, event_id, role)
    end
  end

  defp admin_user?(roles), do: "ADMIN" in roles

  defp seller_and_user_access?(event_id, role, user_id, seller_id) do
    user_has_permission? = has_event_permission(user_id, event_id, role)
    seller_has_access? = has_seller_access_to_event?(seller_id, event_id)
    access? = seller_has_access? && user_has_permission?

    if access? do
      Logger.debug(
        "User #{inspect(user_id)} has seller organizer access and user permission for event #{inspect(event_id)}"
      )
    else
      Logger.warning(
        "User #{inspect(user_id)} lacks required access to event #{inspect(event_id)} " <>
          "with role #{inspect(role)}. " <>
          "Seller access: #{seller_has_access?}, User permission: #{user_has_permission?}"
      )
    end

    access?
  end

  # Helper function to check if a seller has access to a specific event
  # Only returns true if the seller is an ORGANIZER and its promoter's
  # created_by_document_id has EventPermission to the event
  defp has_seller_access_to_event?(seller_id, event_id) do
    seller_id
    |> SellerPermissions.base_permission_query()
    |> where([event_permission: ep], ep.event_id == ^event_id)
    |> select([], 1)
    |> Repo.exists?()
  end
end
