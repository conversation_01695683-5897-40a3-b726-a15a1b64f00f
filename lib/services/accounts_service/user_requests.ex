defmodule ExServiceClient.Services.AccountsService.UserRequests do
  @moduledoc """
  """
  use ExServiceClient.Clients.BaseClient

  def get_user_by_firestore_id(firestore_id) do
    [endpoint: :accounts]
    |> client()
    |> Tesla.get("/userinfo/#{firestore_id}/migrate")
    |> parse_response()
  end

  def get_user_by_id(user_id, opts \\ []) do
    # URL-encode the user_id to handle special characters like '/'
    encoded_user_id = URI.encode_www_form(user_id)

    [[endpoint: :accounts] ++ opts]
    |> List.flatten()
    |> client()
    |> Tesla.get("/services/users/#{encoded_user_id}/userinfo")
    |> parse_response()
  end

  @spec create_security_user() :: {:ok, map()} | {:error, any()}
  @spec create_security_user(String.t() | nil) :: {:ok, map()} | {:error, any()}
  def create_security_user(external_id \\ nil) do
    params =
      if is_nil(external_id) do
        %{}
      else
        %{external_id: external_id}
      end

    [endpoint: :accounts]
    |> client()
    |> Tesla.post("/services/users/security/create", params)
    |> parse_response()
  end

  def get_user_by_email(email) do
    [endpoint: :accounts]
    |> client()
    |> Tesla.get("/services/users/userinfo/#{email}")
    |> parse_response()
  end

  def update_user_claims(id, roles) do
    [endpoint: :accounts]
    |> client()
    |> Tesla.patch("/services/users/#{id}/claims", %{roles: roles})
    |> parse_response()
  end
end
