defmodule AccountsService.Firebase.FirebaseAdmin do
  @moduledoc """
  Firebase Admin API
  """

  use AccountsService.Firebase.FirebaseBaseClient

  @max_expiry_seconds 60 * 60
  @aud "https://identitytoolkit.googleapis.com/google.identity.identitytoolkit.v1.IdentityToolkit"
  @alg "RS256"

  @spec generate_email_verification_link(any()) :: {:error, any()} | {:ok, any()}
  def generate_email_verification_link(email) do
    attrs =
      %{
        requestType: "VERIFY_EMAIL",
        email: email,
        returnOobLink: true
      }

    true
    |> client()
    |> Tesla.post("/accounts:sendOobCode", attrs)
    |> handle_response()
  end

  @spec generate_signin_link(any()) :: {:error, any()} | {:ok, any()}
  def generate_signin_link(email) do
    frontend_url = Application.get_env(:accounts_service, :frontend_url)

    attrs =
      %{
        requestType: "EMAIL_SIGNIN",
        continueUrl: "#{frontend_url}/signin?email=#{String.replace(email, "+", "%2B")}",
        email: email,
        returnOobLink: true
      }

    true
    |> client()
    |> Tesla.post("/accounts:sendOobCode", attrs)
    |> handle_response()
  end

  @spec generate_password_reset_link(any()) :: {:error, any()} | {:ok, any()}
  def generate_password_reset_link(email) do
    frontend_url = Application.get_env(:accounts_service, :frontend_url)

    attrs =
      %{
        requestType: "PASSWORD_RESET",
        continueUrl: "#{frontend_url}/change-password",
        email: email,
        returnOobLink: true
      }

    true
    |> client()
    |> Tesla.post("/accounts:sendOobCode", attrs)
    |> handle_response()
  end

  @spec get_user_by_phone_number(any()) :: {:error, any()} | {:ok, any()}
  def get_user_by_phone_number(phone_number) do
    true
    |> client()
    |> Tesla.post("/accounts:lookup", %{"phoneNumber" => phone_number})
    |> handle_response()
  end

  @spec get_user_by_email(any()) :: {:error, any()} | {:ok, any()}
  def get_user_by_email(email) do
    true
    |> client()
    |> Tesla.post("/accounts:lookup", %{"email" => email})
    |> handle_response()
  end

  @spec get_user_by_id(any()) :: {:error, any()} | {:ok, any()}
  def get_user_by_id(uid) do
    true
    |> client()
    |> Tesla.post("/accounts:lookup", %{"localId" => [uid]})
    |> handle_response()
  end

  @spec get_users_by_ids([any()]) :: {:error, any()} | {:ok, any()}
  def get_users_by_ids(uids) do
    true
    |> client()
    |> Tesla.post("/accounts:lookup", %{"localId" => uids})
    |> handle_response()
  end

  @spec create_security_user() :: {:error, any()} | {:ok, any()}
  @spec create_security_user(any()) :: {:error, any()} | {:ok, any()}
  def create_security_user(uid \\ nil)

  def create_security_user(nil) do
    ("sec_" <> (16 |> :crypto.strong_rand_bytes() |> Base.encode16()))
    |> String.downcase()
    |> create_security_user()
  end

  def create_security_user(uid) do
    true
    |> client()
    |> Tesla.post("/accounts:signUp", %{"localId" => uid})
    |> handle_response()
  end

  @spec set_custom_user_claims(any(), map()) :: {:error, any()} | {:ok, any()}
  def set_custom_user_claims(uid, claims) do
    claims = Jason.encode!(claims)

    true
    |> client()
    |> Tesla.post("/accounts:update", %{"localId" => uid, "customAttributes" => claims})
    |> handle_response()
  end

  @spec delete_user(String.t()) :: {:error, any()} | {:ok, any()}
  def delete_user(uid) do
    true
    |> client()
    |> Tesla.post("/accounts:delete", %{"localId" => uid})
    |> handle_response()
  end

  @spec generate_custom_token(any()) ::
          {:error, atom() | [{any(), any()}] | Jason.DecodeError.t()}
          | {:ok, any()}
          | {:ok, binary(), %{optional(binary()) => any()}}
  def generate_custom_token(uid) do
    credentials =
      "SERVICE_ACCOUNT"
      |> Secrets.get_secret()
      |> Jason.decode()

    case credentials do
      {:ok, %{"client_email" => client_email, "private_key" => private_key}} ->
        signer = Joken.Signer.create(@alg, %{"pem" => private_key})

        token_config =
          [default_exp: @max_expiry_seconds, skip: [:jti, :nbf], iss: client_email, aud: @aud]
          |> Joken.Config.default_claims()
          |> Joken.Config.add_claim("sub", fn -> client_email end, &(&1 == client_email))

        Joken.generate_and_sign(
          token_config,
          %{"uid" => uid},
          signer
        )

      error ->
        error
    end
  end
end
