defmodule AccountsService.Address do
  @moduledoc false

  import Ecto.Query

  alias AccountsService.Addresses.Address
  alias AccountsService.Addresses.Country
  alias AccountsService.Repo

  @spec get_country_by_iso(iso :: String.t() | nil) :: Country.t() | nil
  def get_country_by_iso(nil), do: nil
  def get_country_by_iso(iso), do: Repo.one(from c in Country, where: c.iso == ^iso and is_nil(c.deleted_at))

  @spec search_address(address_params :: map() | nil, user_id :: Ecto.UUID.t() | nil) :: Address.t() | nil
  def search_address(
        %{
          "street_address" => street_address,
          "postal_code" => postal_code,
          "locality" => locality,
          "country" => %{"iso" => country_iso},
          "company" => company
        },
        user_id
      ),
      do:
        search_address(
          %{
            street_address: street_address,
            postal_code: postal_code,
            locality: locality,
            country_iso: country_iso,
            company: company
          },
          user_id
        )

  def search_address(
        %{
          street_address: street_address,
          postal_code: postal_code,
          locality: locality,
          country_iso: country_iso,
          company: company
        },
        user_id
      ) do
    from(a in Address,
      as: :address
    )
    |> filter_address_query(:user_id, user_id)
    |> filter_address_query(:street_address, street_address)
    |> filter_address_query(:postal_code, postal_code)
    |> filter_address_query(:locality, locality)
    |> filter_address_query(:country_iso, country_iso)
    |> filter_address_query(:company, company)
    |> where([address: a], is_nil(a.deleted_at))
    |> order_by([address: a], desc: a.updated_at)
    |> limit(1)
    |> Repo.one()
  end

  @spec maybe_create_address(address_params :: map() | nil, user_id :: Ecto.UUID.t() | nil) ::
          {:ok, Address.t()} | {:ok, %{id: nil}}
  def maybe_create_address(address_params), do: maybe_create_address(address_params, nil)
  # return a tuple with id nil to pattern match in the caller
  def maybe_create_address(nil, _user_id), do: {:ok, %{id: nil}}

  def maybe_create_address(address_params, user_id) do
    case search_address(address_params, user_id) do
      nil ->
        address =
          %{}
          |> Map.put(:user_id, user_id)
          |> Map.put(:street_address, Map.get(address_params, "street_address"))
          |> Map.put(:postal_code, Map.get(address_params, "postal_code"))
          |> Map.put(:locality, Map.get(address_params, "locality"))
          |> Map.put(:company, Map.get(address_params, "company"))
          |> Map.put(
            :country_iso,
            get_country_iso_from_address(address_params)
          )

        %Address{}
        |> Address.changeset(address)
        |> Repo.insert()

      address ->
        {:ok, address}
    end
  end

  defp get_country_iso_from_address(%{"country" => %{"iso" => iso}}), do: iso
  defp get_country_iso_from_address(%{"country_iso" => iso}), do: iso
  defp get_country_iso_from_address(_address), do: nil

  defp filter_address_query(query, column, nil), do: where(query, [address: a], is_nil(field(a, ^column)))
  defp filter_address_query(query, column, value), do: where(query, [address: a], ^[{column, value}])
end
