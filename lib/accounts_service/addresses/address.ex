defmodule AccountsService.Addresses.Address do
  @moduledoc false
  use Ecto.Schema

  import Ecto.Changeset

  alias AccountsService.Addresses.Country

  # styler:sort
  @type t ::
          %__MODULE__{
            company: String.t() | nil,
            country_iso: String.t(),
            deleted_at: DateTime.t() | nil,
            id: Ecto.UUID.t(),
            inserted_at: DateTime.t(),
            locality: String.t(),
            postal_code: String.t(),
            region: String.t(),
            street_address: String.t(),
            updated_at: DateTime.t(),
            user_id: Ecto.UUID.t()
          }

  @schema_prefix :accounts
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  # styler:sort
  schema "addresses" do
    belongs_to :country, Country, references: :iso, foreign_key: :country_iso, type: :string
    belongs_to :user, AccountsService.Users.User
    field :company, :string
    field :deleted_at, :utc_datetime
    field :locality, :string
    field :postal_code, :string
    field :region, :string
    field :street_address, :string

    timestamps()
  end

  @doc false
  def changeset(address, attrs) do
    address
    |> cast(
      attrs,
      # styler:sort
      [:company, :country_iso, :deleted_at, :locality, :postal_code, :region, :street_address, :user_id]
    )
    |> validate_required(
      # styler:sort
      [:country_iso, :locality, :postal_code]
    )
  end
end
